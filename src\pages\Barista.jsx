import React, { useState, useMemo, useCallback } from "react";
import { useOrder, ORDER_STATUS } from "../context/OrderContext";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faClock,
  faCheck,
  faCoffee,
  faUtensils,
  faBell,
  faEye,
  faRefresh,
  faTrash,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";

// Memoized Order Card Component to prevent unnecessary re-renders
const OrderCard = React.memo(
  ({
    order,
    onStatusUpdate,
    onViewOrder,
    getStatusColor,
    getStatusText,
    getTimeSinceOrder,
  }) => {
    return (
      <div
        className={`order-card ${order.status}`}
        data-order-id={order.id}
        onClick={() => onViewOrder(order)}
      >
        <div className="order-header">
          <div className="order-info">
            <h3>طاولة {order.tableNumber}</h3>
            <span className="order-time">
              <FontAwesomeIcon icon={faClock} />
              {getTimeSinceOrder(order.timestamp)}
            </span>
          </div>
          <div
            className="order-status"
            style={{ backgroundColor: getStatusColor(order.status) }}
          >
            {getStatusText(order.status)}
          </div>
        </div>

        <div className="order-items">
          {order.items && order.items.length > 0 ? (
            order.items.slice(0, 3).map((item, index) => (
              <div key={index} className="order-item">
                <span>
                  {item.qty || 1}x {item.name || "منتج غير محدد"}
                </span>
              </div>
            ))
          ) : (
            <div className="no-items">لا توجد عناصر</div>
          )}
          {order.items && order.items.length > 3 && (
            <div className="more-items">+{order.items.length - 3} عنصر آخر</div>
          )}
        </div>

        <div className="order-total">المجموع: {order.total.toFixed(2)} ر.س</div>

        <div className="order-actions">
          {order.status === ORDER_STATUS.PENDING && (
            <button
              className="confirm-btn"
              onClick={(e) => {
                e.stopPropagation();
                onStatusUpdate(order.id, ORDER_STATUS.CONFIRMED);
              }}
            >
              <FontAwesomeIcon icon={faCheck} />
              تأكيد
            </button>
          )}
          {order.status === ORDER_STATUS.CONFIRMED && (
            <button
              className="prepare-btn"
              onClick={(e) => {
                e.stopPropagation();
                onStatusUpdate(order.id, ORDER_STATUS.PREPARING);
              }}
            >
              <FontAwesomeIcon icon={faUtensils} />
              بدء التحضير
            </button>
          )}
          {order.status === ORDER_STATUS.PREPARING && (
            <button
              className="ready-btn"
              onClick={(e) => {
                e.stopPropagation();
                onStatusUpdate(order.id, ORDER_STATUS.READY);
              }}
            >
              <FontAwesomeIcon icon={faCheck} />
              جاهز
            </button>
          )}
          <button
            className="view-btn"
            onClick={(e) => {
              e.stopPropagation();
              onViewOrder(order);
            }}
          >
            <FontAwesomeIcon icon={faEye} />
            عرض
          </button>
          {(order.status === ORDER_STATUS.PENDING ||
            order.status === ORDER_STATUS.CONFIRMED) && (
            <button
              className="cancel-btn"
              onClick={(e) => {
                e.stopPropagation();
                if (window.confirm("هل أنت متأكد من إلغاء هذا الطلب؟")) {
                  onStatusUpdate(order.id, ORDER_STATUS.CANCELLED);
                }
              }}
            >
              <FontAwesomeIcon icon={faTimes} />
              إلغاء
            </button>
          )}
        </div>
      </div>
    );
  }
);

const Barista = () => {
  const { state, actions } = useOrder();
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [notification, setNotification] = useState(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Close all modals
  const closeAllModals = () => {
    setSelectedOrder(null);
  };

  // Show notification
  const showNotification = (message, type = "success") => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  const [filter, setFilter] = useState("all");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [lastOrderCount, setLastOrderCount] = useState(0);

  // Update time every minute to refresh time displays
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute instead of every second

    return () => clearInterval(interval);
  }, []);

  // Track new orders and show notifications
  React.useEffect(() => {
    const currentOrderCount = state.orders.filter(
      (order) => order.status === ORDER_STATUS.PENDING
    ).length;

    if (lastOrderCount > 0 && currentOrderCount > lastOrderCount) {
      const newOrdersCount = currentOrderCount - lastOrderCount;
      showNotification(`${newOrdersCount} طلب جديد وصل!`, "success");

      // Play notification sound (optional)
      try {
        const audio = new Audio(
          "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
        );
        audio.play().catch(() => {}); // Ignore errors if audio fails
      } catch (e) {
        // Ignore audio errors
      }
    }

    setLastOrderCount(currentOrderCount);
  }, [state.orders, lastOrderCount]);

  // Memoized filtered orders to prevent unnecessary recalculations
  const filteredOrders = useMemo(() => {
    switch (filter) {
      case "pending":
        return state.orders.filter(
          (order) => order.status === ORDER_STATUS.PENDING
        );
      case "confirmed":
        return state.orders.filter(
          (order) => order.status === ORDER_STATUS.CONFIRMED
        );
      case "preparing":
        return state.orders.filter(
          (order) => order.status === ORDER_STATUS.PREPARING
        );
      case "ready":
        return state.orders.filter(
          (order) => order.status === ORDER_STATUS.READY
        );
      case "cancelled":
        return state.orders.filter(
          (order) => order.status === ORDER_STATUS.CANCELLED
        );
      default:
        return state.orders.filter(
          (order) =>
            order.status !== ORDER_STATUS.COMPLETED &&
            order.status !== ORDER_STATUS.CANCELLED
        );
    }
  }, [state.orders, filter, forceUpdate]);

  // Memoized order counts to prevent recalculation
  const orderCounts = useMemo(() => {
    return {
      pending: state.orders.filter((o) => o.status === ORDER_STATUS.PENDING)
        .length,
      confirmed: state.orders.filter((o) => o.status === ORDER_STATUS.CONFIRMED)
        .length,
      preparing: state.orders.filter((o) => o.status === ORDER_STATUS.PREPARING)
        .length,
      ready: state.orders.filter((o) => o.status === ORDER_STATUS.READY).length,
      cancelled: state.orders.filter((o) => o.status === ORDER_STATUS.CANCELLED)
        .length,
    };
  }, [state.orders, forceUpdate]);

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case ORDER_STATUS.PENDING:
        return "#ff9800";
      case ORDER_STATUS.CONFIRMED:
        return "#2196f3";
      case ORDER_STATUS.PREPARING:
        return "#9c27b0";
      case ORDER_STATUS.READY:
        return "#4caf50";
      case ORDER_STATUS.CANCELLED:
        return "#dc3545";
      default:
        return "#757575";
    }
  };

  // Get status text in Arabic
  const getStatusText = (status) => {
    switch (status) {
      case ORDER_STATUS.PENDING:
        return "في الانتظار";
      case ORDER_STATUS.CONFIRMED:
        return "مؤكد";
      case ORDER_STATUS.PREPARING:
        return "قيد التحضير";
      case ORDER_STATUS.READY:
        return "جاهز";
      case ORDER_STATUS.COMPLETED:
        return "مكتمل";
      case ORDER_STATUS.CANCELLED:
        return "ملغي";
      default:
        return status;
    }
  };

  // Optimized status update handler with immediate feedback
  const handleStatusUpdate = useCallback(
    async (orderId, newStatus) => {
      try {
        // Show immediate feedback
        const orderElement = document.querySelector(
          `[data-order-id="${orderId}"]`
        );
        if (orderElement) {
          orderElement.style.opacity = "0.7";
          orderElement.style.pointerEvents = "none";
        }

        // Update the order status
        actions.updateOrderStatus(orderId, newStatus);

        // Close modal after a short delay to show the update
        setTimeout(() => {
          closeAllModals();

          // Restore element state
          if (orderElement) {
            orderElement.style.opacity = "1";
            orderElement.style.pointerEvents = "auto";
          }
        }, 300);

        // Show success notification
        const statusText = getStatusText(newStatus);
        showNotification(`تم تحديث الطلب إلى: ${statusText}`, "success");
      } catch (error) {
        console.error("خطأ في تحديث حالة الطلب:", error);
        showNotification("حدث خطأ في تحديث الطلب", "error");

        // Restore element state on error
        const orderElement = document.querySelector(
          `[data-order-id="${orderId}"]`
        );
        if (orderElement) {
          orderElement.style.opacity = "1";
          orderElement.style.pointerEvents = "auto";
        }
      }
    },
    [actions, getStatusText]
  );

  // Memoized time calculation to reduce CPU usage
  const getTimeSinceOrder = useCallback(
    (timestamp) => {
      const orderTime = new Date(timestamp);
      const diffInMinutes = Math.floor((currentTime - orderTime) / (1000 * 60));

      if (diffInMinutes < 1) return "الآن";
      if (diffInMinutes < 60) return `${diffInMinutes} دقيقة`;
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ساعة`;
    },
    [currentTime]
  );

  return (
    <div className="barista-container">
      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          {notification.message}
        </div>
      )}
      <div className="barista-header">
        <h1>
          <FontAwesomeIcon icon={faCoffee} />
          لوحة الباريستا
        </h1>
        <div className="header-actions">
          {/* إشعار الطلبات الجديدة */}
          {orderCounts.pending > 0 && (
            <div className="notification-badge">
              <FontAwesomeIcon icon={faBell} />
              {orderCounts.pending} طلب جديد
            </div>
          )}

          {/* مجموعة الأزرار الأساسية */}
          <div className="action-buttons-group">
            <button
              className="refresh-btn secondary-btn"
              onClick={() => {
                showNotification("تم تحديث البيانات", "success");
                // Force re-render without page reload
                window.location.hash = Math.random();
                setTimeout(() => {
                  window.location.hash = "";
                }, 100);
              }}
              title="تحديث البيانات"
            >
              <FontAwesomeIcon icon={faRefresh} />
              تحديث
            </button>
          </div>

          {/* مجموعة الأزرار الثانوية */}
          <div className="secondary-buttons-group">
            <button
              className="clear-btn danger-btn"
              onClick={() => {
                if (
                  window.confirm(
                    "هل أنت متأكد من مسح جميع البيانات؟ سيتم حذف جميع الطلبات والمبيعات!"
                  )
                ) {
                  // Clear all data from localStorage
                  localStorage.removeItem("cafeOrders");
                  localStorage.removeItem("waiterSales");
                  localStorage.removeItem("cafeSales");

                  // Trigger storage events for immediate update
                  window.dispatchEvent(
                    new StorageEvent("storage", {
                      key: "cafeOrders",
                      newValue: null,
                      oldValue: "[]",
                    })
                  );

                  // Force component re-render to update the UI
                  setForceUpdate((prev) => prev + 1);

                  showNotification("تم مسح جميع البيانات بنجاح", "warning");

                  // No page refresh - let React context handle the update
                  // The storage event will trigger automatic update
                }
              }}
              title="مسح جميع البيانات"
            >
              <FontAwesomeIcon icon={faTrash} />
              مسح البيانات
            </button>
          </div>
        </div>
      </div>

      <div className="barista-filters">
        <button
          className={filter === "all" ? "active" : ""}
          onClick={() => setFilter("all")}
        >
          جميع الطلبات ({filteredOrders.length})
        </button>
        <button
          className={filter === "pending" ? "active" : ""}
          onClick={() => setFilter("pending")}
        >
          في الانتظار ({orderCounts.pending})
        </button>
        <button
          className={filter === "confirmed" ? "active" : ""}
          onClick={() => setFilter("confirmed")}
        >
          مؤكد ({orderCounts.confirmed})
        </button>
        <button
          className={filter === "preparing" ? "active" : ""}
          onClick={() => setFilter("preparing")}
        >
          قيد التحضير ({orderCounts.preparing})
        </button>
        <button
          className={filter === "ready" ? "active" : ""}
          onClick={() => setFilter("ready")}
        >
          جاهز ({orderCounts.ready})
        </button>
        <button
          className={filter === "cancelled" ? "active" : ""}
          onClick={() => setFilter("cancelled")}
        >
          ملغي ({orderCounts.cancelled})
        </button>
      </div>

      <div className="barista-content">
        <div className="orders-grid">
          {filteredOrders.length === 0 ? (
            <div className="no-orders">
              <FontAwesomeIcon icon={faCoffee} size="3x" />
              <p>لا توجد طلبات</p>
            </div>
          ) : (
            filteredOrders.map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                onStatusUpdate={handleStatusUpdate}
                onViewOrder={setSelectedOrder}
                getStatusColor={getStatusColor}
                getStatusText={getStatusText}
                getTimeSinceOrder={getTimeSinceOrder}
              />
            ))
          )}
        </div>

        {selectedOrder && (
          <div
            className="order-modal-overlay barista-order-modal"
            onClick={closeAllModals}
          >
            <div
              className="barista-order-content"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="modal-header">
                <h2>تفاصيل الطلب - طاولة {selectedOrder.tableNumber}</h2>
                <button className="close-btn" onClick={closeAllModals}>
                  ×
                </button>
              </div>

              <div className="modal-content">
                <div className="order-details">
                  <p>
                    <strong>الوقت:</strong>{" "}
                    {new Date(selectedOrder.timestamp).toLocaleString("ar-SA")}
                  </p>
                  <p>
                    <strong>الحالة:</strong>{" "}
                    {getStatusText(selectedOrder.status)}
                  </p>
                  {selectedOrder.customerName && (
                    <p>
                      <strong>اسم العميل:</strong> {selectedOrder.customerName}
                    </p>
                  )}
                  {selectedOrder.notes && (
                    <p>
                      <strong>ملاحظات:</strong> {selectedOrder.notes}
                    </p>
                  )}
                </div>

                <div className="order-items-detail">
                  <h3>العناصر المطلوبة:</h3>

                  {(() => {
                    // التحقق من وجود العناصر مع معالجة شاملة
                    const items = selectedOrder.items || [];
                    const hasValidItems =
                      Array.isArray(items) && items.length > 0;

                    if (!hasValidItems) {
                      // إذا لم توجد عناصر، نحاول إنشاء عناصر تجريبية بناءً على بيانات الطلب
                      const mockItems = [];
                      if (selectedOrder.total && selectedOrder.total > 0) {
                        // إنشاء عنصر تجريبي بناءً على المجموع
                        mockItems.push({
                          id: "mock-item-1",
                          name: "عنصر من الطلب",
                          desc: "تفاصيل العنصر غير متوفرة - يرجى التحقق من الطلب",
                          qty: 1,
                          price:
                            selectedOrder.subtotal || selectedOrder.total || 0,
                        });
                      }

                      if (mockItems.length > 0) {
                        return mockItems.map((item, index) => (
                          <div
                            key={index}
                            className="item-detail"
                            style={{
                              backgroundColor: "#fff3cd",
                              border: "1px solid #ffeaa7",
                            }}
                          >
                            <div className="item-info">
                              <span className="item-name">{item.name}</span>
                              <span
                                className="item-desc"
                                style={{ color: "#856404" }}
                              >
                                {item.desc}
                              </span>
                            </div>
                            <div className="item-qty-price">
                              <span className="qty">{item.qty}x</span>
                              <span className="price">
                                {(item.qty * item.price).toFixed(2)} ر.س
                              </span>
                            </div>
                          </div>
                        ));
                      }

                      return (
                        <div className="no-items-message">
                          <p>⚠️ لا توجد تفاصيل العناصر</p>
                          <p
                            style={{
                              fontSize: "12px",
                              color: "#666",
                              marginTop: "10px",
                            }}
                          >
                            المجموع: {selectedOrder.total?.toFixed(2) || "0.00"}{" "}
                            ر.س
                            <br />
                            يرجى التحقق من الطلب مع العميل
                          </p>
                        </div>
                      );
                    }

                    return items.map((item, index) => (
                      <div key={index} className="item-detail">
                        <div className="item-info">
                          <span className="item-name">
                            {item.name || "منتج غير محدد"}
                          </span>
                          {item.desc && (
                            <span className="item-desc">{item.desc}</span>
                          )}
                        </div>
                        <div className="item-qty-price">
                          <span className="qty">{item.qty || 1}x</span>
                          <span className="price">
                            {((item.qty || 1) * (item.price || 0)).toFixed(2)}{" "}
                            ر.س
                          </span>
                        </div>
                      </div>
                    ));
                  })()}
                </div>

                <div className="order-summary">
                  <div className="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>{selectedOrder.subtotal.toFixed(2)} ر.س</span>
                  </div>
                  <div className="summary-row">
                    <span>الضريبة:</span>
                    <span>{selectedOrder.tax.toFixed(2)} ر.س</span>
                  </div>
                  <div className="summary-row total">
                    <span>الإجمالي:</span>
                    <span>{selectedOrder.total.toFixed(2)} ر.س</span>
                  </div>
                </div>

                <div className="modal-actions">
                  {selectedOrder.status === ORDER_STATUS.PENDING && (
                    <button
                      className="confirm-btn"
                      onClick={() =>
                        handleStatusUpdate(
                          selectedOrder.id,
                          ORDER_STATUS.CONFIRMED
                        )
                      }
                    >
                      تأكيد الطلب
                    </button>
                  )}
                  {selectedOrder.status === ORDER_STATUS.CONFIRMED && (
                    <button
                      className="prepare-btn"
                      onClick={() =>
                        handleStatusUpdate(
                          selectedOrder.id,
                          ORDER_STATUS.PREPARING
                        )
                      }
                    >
                      بدء التحضير
                    </button>
                  )}
                  {selectedOrder.status === ORDER_STATUS.PREPARING && (
                    <button
                      className="ready-btn"
                      onClick={() =>
                        handleStatusUpdate(selectedOrder.id, ORDER_STATUS.READY)
                      }
                    >
                      الطلب جاهز
                    </button>
                  )}
                  {(selectedOrder.status === ORDER_STATUS.PENDING ||
                    selectedOrder.status === ORDER_STATUS.CONFIRMED) && (
                    <button
                      className="cancel-order-btn danger-btn"
                      onClick={() => {
                        if (
                          window.confirm("هل أنت متأكد من إلغاء هذا الطلب؟")
                        ) {
                          handleStatusUpdate(
                            selectedOrder.id,
                            ORDER_STATUS.CANCELLED
                          );
                        }
                      }}
                    >
                      <FontAwesomeIcon icon={faTimes} />
                      إلغاء الطلب
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Barista;
