-- إصلاح جدول الطلبات مع أنواع البيانات الصحيحة
-- تم إصلاح المشاكل التالية:
-- 1. table_number: من VARCHAR إلى INTEGER (إزالة علامات الاقتباس)
-- 2. subtotal, tax, total: من VARCHAR إلى DECIMAL (إزالة علامات الاقتباس)
-- 3. تحسين التنسيق للقراءة
-- 4. استخدام UPSERT لتجنب تضارب المفاتيح

-- الطريقة 1: تحديث السجلات الموجودة بدلاً من إدراج جديدة
UPDATE "public"."orders" SET
    "table_number" = 1,
    "customer_name" = 'زبون طاولة 1',
    "notes" = '',
    "twitter_handle" = null,
    "subtotal" = 15.00,
    "tax" = 2.25,
    "total" = 17.25,
    "status" = 'cancelled',
    "assigned_waiter" = null,
    "waiter_id" = null,
    "updated_at" = '2025-06-16 20:34:53.062251+00'
WHERE "id" = '6aa1ded8-12de-4cc1-9b60-1df22941f2f4';

UPDATE "public"."orders" SET
    "table_number" = 2,
    "customer_name" = '',
    "notes" = '',
    "twitter_handle" = null,
    "subtotal" = 32.00,
    "tax" = 4.80,
    "total" = 36.80,
    "status" = 'confirmed',
    "assigned_waiter" = null,
    "waiter_id" = null,
    "updated_at" = '2025-06-17 19:07:18.678453+00'
WHERE "id" = 'da750940-85e0-43f4-bfa4-d1858237e33c';

UPDATE "public"."orders" SET
    "table_number" = 1,
    "customer_name" = '',
    "notes" = '',
    "twitter_handle" = null,
    "subtotal" = 70.00,
    "tax" = 10.50,
    "total" = 80.50,
    "status" = 'pending',
    "assigned_waiter" = null,
    "waiter_id" = null,
    "updated_at" = '2025-06-17 19:49:08.201501+00'
WHERE "id" = 'e7969884-95ea-4fba-a0f8-315b21986e5b';

-- الطريقة 2: استخدام UPSERT (INSERT ... ON CONFLICT)
INSERT INTO "public"."orders" (
    "id",
    "table_number",
    "customer_name",
    "notes",
    "twitter_handle",
    "subtotal",
    "tax",
    "total",
    "status",
    "assigned_waiter",
    "waiter_id",
    "created_at",
    "updated_at"
) VALUES
(
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4',
    1,                          -- ✅ رقم صحيح بدون علامات اقتباس
    'زبون طاولة 1',
    '',
    null,
    15.00,                      -- ✅ رقم عشري صحيح
    2.25,                       -- ✅ رقم عشري صحيح
    17.25,                      -- ✅ رقم عشري صحيح
    'cancelled',
    null,
    null,
    '2025-06-16 17:36:09.854332+00',
    '2025-06-16 20:34:53.062251+00'
),
(
    'da750940-85e0-43f4-bfa4-d1858237e33c',
    2,                          -- ✅ رقم صحيح بدون علامات اقتباس
    '',
    '',
    null,
    32.00,                      -- ✅ رقم عشري صحيح
    4.80,                       -- ✅ رقم عشري صحيح
    36.80,                      -- ✅ رقم عشري صحيح
    'confirmed',
    null,
    null,
    '2025-06-17 18:51:40.311111+00',
    '2025-06-17 19:07:18.678453+00'
),
(
    'e7969884-95ea-4fba-a0f8-315b21986e5b',
    1,                          -- ✅ رقم صحيح بدون علامات اقتباس
    '',
    '',
    null,
    70.00,                      -- ✅ رقم عشري صحيح
    10.50,                      -- ✅ رقم عشري صحيح
    80.50,                      -- ✅ رقم عشري صحيح
    'pending',
    null,
    null,
    '2025-06-17 19:49:08.201501+00',
    '2025-06-17 19:49:08.201501+00'
)
ON CONFLICT ("id")
DO UPDATE SET
    "table_number" = EXCLUDED."table_number",
    "customer_name" = EXCLUDED."customer_name",
    "notes" = EXCLUDED."notes",
    "twitter_handle" = EXCLUDED."twitter_handle",
    "subtotal" = EXCLUDED."subtotal",
    "tax" = EXCLUDED."tax",
    "total" = EXCLUDED."total",
    "status" = EXCLUDED."status",
    "assigned_waiter" = EXCLUDED."assigned_waiter",
    "waiter_id" = EXCLUDED."waiter_id",
    "updated_at" = EXCLUDED."updated_at";

-- التحقق من النتيجة
SELECT
    id,
    table_number,
    customer_name,
    subtotal,
    tax,
    total,
    status,
    pg_typeof(table_number) as table_number_type,
    pg_typeof(subtotal) as subtotal_type
FROM "public"."orders"
WHERE id IN (
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4',
    'da750940-85e0-43f4-bfa4-d1858237e33c',
    'e7969884-95ea-4fba-a0f8-315b21986e5b'
)
ORDER BY created_at DESC;

-- للتحقق من البيانات المدخلة:
-- SELECT * FROM "public"."orders" ORDER BY "created_at" DESC;

-- ملاحظات مهمة:
-- 1. تأكد من أن جدول orders له البنية الصحيحة:
--    - table_number: INTEGER
--    - subtotal, tax, total: DECIMAL أو NUMERIC
-- 2. إذا كان الجدول يحتوي على قيود (constraints)، تأكد من أنها تسمح بهذه القيم
-- 3. تأكد من أن UUIDs فريدة ولا تتعارض مع بيانات موجودة
