-- إصلاح جدول الطلبات مع أنواع البيانات الصحيحة
-- تم إصلاح المشاكل التالية:
-- 1. table_number: من VARCHAR إلى INTEGER (إزالة علامات الاقتباس)
-- 2. subtotal, tax, total: من VARCHAR إلى DECIMAL (إزالة علامات الاقتباس)
-- 3. تحسين التنسيق للقراءة

INSERT INTO "public"."orders" (
    "id", 
    "table_number", 
    "customer_name", 
    "notes", 
    "twitter_handle", 
    "subtotal", 
    "tax", 
    "total", 
    "status", 
    "assigned_waiter", 
    "waiter_id", 
    "created_at", 
    "updated_at"
) VALUES 
(
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4', 
    1,                          -- ✅ رقم صحيح بدون علامات اقتباس
    'زبون طاولة 1', 
    '', 
    null, 
    15.00,                      -- ✅ رقم عشري صحيح
    2.25,                       -- ✅ رقم عشري صحيح
    17.25,                      -- ✅ رقم عشري صحيح
    'cancelled', 
    null, 
    null, 
    '2025-06-16 17:36:09.854332+00', 
    '2025-06-16 20:34:53.062251+00'
),
(
    'da750940-85e0-43f4-bfa4-d1858237e33c', 
    2,                          -- ✅ رقم صحيح بدون علامات اقتباس
    '', 
    '', 
    null, 
    32.00,                      -- ✅ رقم عشري صحيح
    4.80,                       -- ✅ رقم عشري صحيح
    36.80,                      -- ✅ رقم عشري صحيح
    'confirmed', 
    null, 
    null, 
    '2025-06-17 18:51:40.311111+00', 
    '2025-06-17 19:07:18.678453+00'
),
(
    'e7969884-95ea-4fba-a0f8-315b21986e5b', 
    1,                          -- ✅ رقم صحيح بدون علامات اقتباس
    '', 
    '', 
    null, 
    70.00,                      -- ✅ رقم عشري صحيح
    10.50,                      -- ✅ رقم عشري صحيح
    80.50,                      -- ✅ رقم عشري صحيح
    'pending', 
    null, 
    null, 
    '2025-06-17 19:49:08.201501+00', 
    '2025-06-17 19:49:08.201501+00'
);

-- للتحقق من البيانات المدخلة:
-- SELECT * FROM "public"."orders" ORDER BY "created_at" DESC;

-- ملاحظات مهمة:
-- 1. تأكد من أن جدول orders له البنية الصحيحة:
--    - table_number: INTEGER
--    - subtotal, tax, total: DECIMAL أو NUMERIC
-- 2. إذا كان الجدول يحتوي على قيود (constraints)، تأكد من أنها تسمح بهذه القيم
-- 3. تأكد من أن UUIDs فريدة ولا تتعارض مع بيانات موجودة
