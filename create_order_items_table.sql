-- إنشاء جدول order_items المفقود
-- هذا الجدول ضروري لربط الطلبات بالمنتجات

-- إنشاء جدول order_items
CREATE TABLE IF NOT EXISTS "public"."order_items" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "order_id" UUID NOT NULL,
    "product_id" UUID,
    "product_name" VARCHAR(255),
    "product_description" TEXT,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "unit_price" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "total_price" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS "idx_order_items_order_id" ON "public"."order_items" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_order_items_product_id" ON "public"."order_items" ("product_id");

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_order_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_order_items_updated_at 
    BEFORE UPDATE ON "public"."order_items" 
    FOR EACH ROW 
    EXECUTE FUNCTION update_order_items_updated_at();

-- إنشاء trigger لحساب total_price تلقائياً
CREATE OR REPLACE FUNCTION calculate_order_items_total()
RETURNS TRIGGER AS $$
BEGIN
    NEW.total_price = NEW.unit_price * NEW.quantity;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_order_items_total 
    BEFORE INSERT OR UPDATE ON "public"."order_items" 
    FOR EACH ROW 
    EXECUTE FUNCTION calculate_order_items_total();

-- إضافة قيود للتحقق من صحة البيانات
ALTER TABLE "public"."order_items" 
ADD CONSTRAINT IF NOT EXISTS "check_quantity_positive" 
CHECK ("quantity" > 0);

ALTER TABLE "public"."order_items" 
ADD CONSTRAINT IF NOT EXISTS "check_unit_price_non_negative" 
CHECK ("unit_price" >= 0);

-- إضافة مفتاح خارجي للربط مع جدول orders (إذا كان موجوداً)
-- ALTER TABLE "public"."order_items" 
-- ADD CONSTRAINT "fk_order_items_order_id" 
-- FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE CASCADE;

-- تعليقات على الجدول والأعمدة
COMMENT ON TABLE "public"."order_items" IS 'جدول عناصر الطلبات - يحتوي على المنتجات في كل طلب';
COMMENT ON COLUMN "public"."order_items"."id" IS 'معرف فريد لعنصر الطلب';
COMMENT ON COLUMN "public"."order_items"."order_id" IS 'معرف الطلب (مرجع لجدول orders)';
COMMENT ON COLUMN "public"."order_items"."product_id" IS 'معرف المنتج (مرجع لجدول products)';
COMMENT ON COLUMN "public"."order_items"."product_name" IS 'اسم المنتج (نسخة محفوظة)';
COMMENT ON COLUMN "public"."order_items"."product_description" IS 'وصف المنتج (نسخة محفوظة)';
COMMENT ON COLUMN "public"."order_items"."quantity" IS 'الكمية المطلوبة';
COMMENT ON COLUMN "public"."order_items"."unit_price" IS 'سعر الوحدة';
COMMENT ON COLUMN "public"."order_items"."total_price" IS 'السعر الإجمالي (محسوب تلقائياً)';

-- إدراج بيانات تجريبية للطلبات الموجودة
INSERT INTO "public"."order_items" (
    "order_id",
    "product_id", 
    "product_name",
    "product_description",
    "quantity",
    "unit_price"
) VALUES 
-- عناصر للطلب الأول
('3e13729b-294b-4f36-b3c6-75d7e540a6fd', '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8', 'قهوة تركية', 'قهوة بن عربي أصيلة مع الهيل', 2, 15.00),
('3e13729b-294b-4f36-b3c6-75d7e540a6fd', '8e832479-2020-474d-9061-e68b7582dc29', 'كابتشينو', 'إسبريسو مع حليب مبخر ورغوة', 1, 18.00),

-- عناصر للطلب الثاني  
('0fb01cae-9b51-442b-8107-ca5fbe20b2e6', '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8', 'قهوة تركية', 'قهوة بن عربي أصيلة مع الهيل', 1, 15.00),
('0fb01cae-9b51-442b-8107-ca5fbe20b2e6', 'product-2', 'كرواسون', 'كرواسون فرنسي طازج', 1, 12.00),

-- عناصر للطلب الثالث
('ee3aaf92-8fb9-4704-885c-7a700bb965cb', '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8', 'قهوة تركية', 'قهوة بن عربي أصيلة مع الهيل', 1, 15.00),
('ee3aaf92-8fb9-4704-885c-7a700bb965cb', '8e832479-2020-474d-9061-e68b7582dc29', 'كابتشينو', 'إسبريسو مع حليب مبخر ورغوة', 1, 18.00);

-- عرض النتائج
SELECT 
    'تم إنشاء جدول order_items بنجاح!' as message,
    COUNT(*) as total_items
FROM "public"."order_items";

-- عرض الطلبات مع العناصر
SELECT 
    o.id as order_id,
    o.table_number,
    o.customer_name,
    o.total,
    o.status,
    oi.product_name,
    oi.quantity,
    oi.unit_price,
    oi.total_price
FROM "public"."orders" o
LEFT JOIN "public"."order_items" oi ON o.id = oi.order_id
ORDER BY o.created_at DESC, oi.created_at;

SELECT 'جدول order_items جاهز للاستخدام!' as final_message;
