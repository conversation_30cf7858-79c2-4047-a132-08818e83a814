import React, { useState } from "react";
import { useLanguage } from "../context/LanguageContext";

const MenuItem = ({ item, addToCart }) => {
  const { t, language } = useLanguage();
  const [isAdding, setIsAdding] = useState(false);

  const handleCardClick = async () => {
    if (isAdding) return;

    setIsAdding(true);
    addToCart(item, 1);

    // Reset adding state and show feedback
    setTimeout(() => {
      setIsAdding(false);
    }, 500);
  };

  return (
    <div
      className={`modern-menu-card ${isAdding ? "adding" : ""}`}
      onClick={handleCardClick}
    >
      {/* Product Image */}
      <div className="card-image-container">
        <img
          src={item.image || item.img || "/images/placeholder.svg"}
          alt={item.name}
          className="card-image"
          loading="lazy" // تحميل كسول للأداء
          decoding="async" // فك تشفير غير متزامن
          onError={(e) => {
            e.target.src = "/images/placeholder.svg";
          }}
          onLoad={(e) => {
            // تحسين جودة الصورة بعد التحميل
            e.target.style.opacity = "1";
          }}
          style={{ opacity: 0, transition: "opacity 0.3s ease" }}
        />
      </div>

      {/* Product Content */}
      <div className="card-content">
        <div className="product-info">
          <h3 className="product-title">
            {language === "en" && item.nameEn ? item.nameEn : item.name}
          </h3>
          {(item.desc || item.description) && (
            <p className="product-description">
              {language === "en" && item.descEn
                ? item.descEn
                : item.desc || item.description}
            </p>
          )}
        </div>
        <div className="product-price-section">
          <span className="product-price">
            {item.price} {t.currency || "ريال"}
          </span>
          <div className="click-hint">{t.addToCart || "اضغط للإضافة"}</div>
        </div>
      </div>

      {/* Adding feedback */}
      {isAdding && (
        <div className="adding-overlay">
          <div className="adding-feedback">
            <div className="success-icon">✓</div>
            <span>{t.added || "تم الإضافة"}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuItem;
