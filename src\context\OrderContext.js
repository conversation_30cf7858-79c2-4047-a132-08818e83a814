import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  useRef,
} from "react";
import { v4 as uuidv4 } from "uuid";
import { dataService } from "../services/dataService";

const OrderContext = createContext();

// Order statuses
export const ORDER_STATUS = {
  PENDING: "pending", // طلب جديد وصل للباريستا والنادل
  WAITER_ACCEPTED: "waiter_accepted", // النادل قبل الطلب
  WAITER_REJECTED: "waiter_rejected", // النادل رفض الطلب
  CONFIRMED: "confirmed", // الباريستا أكد الطلب
  PREPARING: "preparing", // الباريستا يحضر الطلب
  READY: "ready", // الطلب جاهز
  COMPLETED: "completed", // تم تسليم الطلب ودفع المبلغ
  CANCELLED: "cancelled", // تم إلغاء الطلب
};

// Action types
const ORDER_ACTIONS = {
  ADD_ORDER: "ADD_ORDER",
  UPDATE_ORDER_STATUS: "UPDATE_ORDER_STATUS",
  WAITER_ACCEPT_ORDER: "WAITER_ACCEPT_ORDER",
  WAITER_REJECT_ORDER: "WAITER_REJECT_ORDER",
  CONFIRM_PAYMENT: "CONFIRM_PAYMENT",
  CANCEL_ORDER: "CANCEL_ORDER",
  SET_ORDERS: "SET_ORDERS",
  SET_SALES: "SET_SALES",
  ADD_TO_CART: "ADD_TO_CART",
  UPDATE_CART_QTY: "UPDATE_CART_QTY",
  REMOVE_FROM_CART: "REMOVE_FROM_CART",
  CLEAR_CART: "CLEAR_CART",
  SET_TABLE_NUMBER: "SET_TABLE_NUMBER",
};

// Initial state
const initialState = {
  orders: [],
  cart: [],
  tableNumber: null,
  sales: [],
};

// Reducer function
function orderReducer(state, action) {
  switch (action.type) {
    case ORDER_ACTIONS.ADD_ORDER:
      const newOrder = {
        id: uuidv4(),
        tableNumber: action.payload.tableNumber,
        items: action.payload.items,
        subtotal: action.payload.subtotal,
        tax: action.payload.tax,
        total: action.payload.total,
        status: ORDER_STATUS.PENDING,
        timestamp: new Date().toISOString(),
        customerName: action.payload.customerName || "",
        notes: action.payload.notes || "",
        twitterHandle: action.payload.twitterHandle || null,
      };
      console.log(
        "✅ Order added to state:",
        newOrder.id,
        "Total orders:",
        state.orders.length + 1
      );
      return {
        ...state,
        orders: [...state.orders, newOrder],
      };

    case ORDER_ACTIONS.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map((order) =>
          order.id === action.payload.orderId
            ? {
                ...order,
                status: action.payload.status,
                updatedAt: new Date().toISOString(),
              }
            : order
        ),
      };

    case ORDER_ACTIONS.CONFIRM_PAYMENT:
      const completedOrder = state.orders.find(
        (order) => order.id === action.payload.orderId
      );
      if (completedOrder) {
        const sale = {
          id: uuidv4(),
          orderId: completedOrder.id,
          tableNumber: completedOrder.tableNumber,
          total: completedOrder.total,
          timestamp: new Date().toISOString(),
          paymentMethod: action.payload.paymentMethod || "cash",
        };
        return {
          ...state,
          orders: state.orders.map((order) =>
            order.id === action.payload.orderId
              ? {
                  ...order,
                  status: ORDER_STATUS.COMPLETED,
                  paidAt: new Date().toISOString(),
                }
              : order
          ),
          sales: [...state.sales, sale],
        };
      }
      return state;

    case ORDER_ACTIONS.WAITER_ACCEPT_ORDER:
      return {
        ...state,
        orders: state.orders.map((order) =>
          order.id === action.payload.orderId
            ? {
                ...order,
                status: ORDER_STATUS.WAITER_ACCEPTED,
                waiterAcceptedAt: new Date().toISOString(),
                acceptedByWaiter: action.payload.waiterName,
              }
            : order
        ),
      };

    case ORDER_ACTIONS.WAITER_REJECT_ORDER:
      return {
        ...state,
        orders: state.orders.map((order) =>
          order.id === action.payload.orderId
            ? {
                ...order,
                status: ORDER_STATUS.WAITER_REJECTED,
                waiterRejectedAt: new Date().toISOString(),
                rejectedByWaiter: action.payload.waiterName,
                rejectionReason: action.payload.reason,
              }
            : order
        ),
      };

    case ORDER_ACTIONS.CANCEL_ORDER:
      return {
        ...state,
        orders: state.orders.map((order) =>
          order.id === action.payload.orderId
            ? {
                ...order,
                status: ORDER_STATUS.CANCELLED,
                cancelledAt: new Date().toISOString(),
              }
            : order
        ),
      };

    case ORDER_ACTIONS.ADD_TO_CART:
      const existingItem = state.cart.find(
        (item) => item.id === action.payload.item.id
      );
      if (existingItem) {
        return {
          ...state,
          cart: state.cart.map((item) =>
            item.id === action.payload.item.id
              ? { ...item, qty: item.qty + action.payload.qty }
              : item
          ),
        };
      }
      return {
        ...state,
        cart: [
          ...state.cart,
          { ...action.payload.item, qty: action.payload.qty },
        ],
      };

    case ORDER_ACTIONS.UPDATE_CART_QTY:
      return {
        ...state,
        cart: state.cart.map((item) =>
          item.id === action.payload.itemId
            ? { ...item, qty: action.payload.qty }
            : item
        ),
      };

    case ORDER_ACTIONS.REMOVE_FROM_CART:
      return {
        ...state,
        cart: state.cart.filter((item) => item.id !== action.payload.itemId),
      };

    case ORDER_ACTIONS.CLEAR_CART:
      return {
        ...state,
        cart: [],
      };

    case ORDER_ACTIONS.SET_TABLE_NUMBER:
      return {
        ...state,
        tableNumber: action.payload.tableNumber,
      };

    case ORDER_ACTIONS.SET_ORDERS:
      return {
        ...state,
        orders: action.payload.orders,
      };

    case ORDER_ACTIONS.SET_SALES:
      return {
        ...state,
        sales: action.payload.sales,
      };

    default:
      return state;
  }
}

// Provider component
export function OrderProvider({ children }) {
  const [state, dispatch] = useReducer(orderReducer, initialState);
  const ordersRef = useRef(state.orders);

  // Update ref when orders change
  useEffect(() => {
    ordersRef.current = state.orders;
  }, [state.orders]);

  // Load data from Supabase on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load orders
        const orders = await dataService.getOrders();
        if (orders && orders.length > 0) {
          dispatch({
            type: ORDER_ACTIONS.SET_ORDERS,
            payload: { orders },
          });
        }

        // Load sales
        const sales = await dataService.getSales();
        if (sales && sales.length > 0) {
          dispatch({
            type: ORDER_ACTIONS.SET_SALES,
            payload: { sales },
          });
        }
      } catch (error) {
        console.error("Error loading data:", error);
        // Initialize with empty arrays if loading fails
        dispatch({
          type: ORDER_ACTIONS.SET_ORDERS,
          payload: { orders: [] },
        });
        dispatch({
          type: ORDER_ACTIONS.SET_SALES,
          payload: { sales: [] },
        });
      }
    };

    loadData();
  }, []);

  // Real-time updates from Supabase (if needed in the future)
  useEffect(() => {
    // This can be extended to listen for real-time updates from Supabase
    // For now, we rely on manual refresh or periodic updates

    const interval = setInterval(async () => {
      try {
        // Periodically refresh orders from Supabase
        const orders = await dataService.getOrders();
        if (orders && orders.length > 0) {
          const currentOrdersString = JSON.stringify(ordersRef.current);
          const newOrdersString = JSON.stringify(orders);

          if (currentOrdersString !== newOrdersString) {
            dispatch({
              type: ORDER_ACTIONS.SET_ORDERS,
              payload: { orders },
            });
          }
        }
      } catch (error) {
        console.error("Error refreshing orders:", error);
      }
    }, 10000); // Check every 10 seconds

    return () => {
      clearInterval(interval);
    };
  }, []);

  // Auto-save to Supabase when orders change (optional)
  useEffect(() => {
    // Skip saving on initial load
    if (state.orders.length === 0) {
      return;
    }

    // Add a small delay to prevent conflicts during rapid updates
    const timeoutId = setTimeout(async () => {
      try {
        console.log(
          "💾 Auto-saving orders to Supabase:",
          state.orders.length,
          "orders"
        );
        // This could be implemented if needed for auto-sync
        // await dataService.syncOrders(state.orders);
      } catch (error) {
        console.error("Error auto-saving orders:", error);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.orders]);

  useEffect(() => {
    // Skip saving on initial load
    if (state.sales.length === 0) {
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        // This could be implemented if needed for auto-sync
        // await dataService.syncSales(state.sales);
      } catch (error) {
        console.error("Error auto-saving sales:", error);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.sales]);

  // Action creators
  const actions = {
    addOrder: async (orderData) => {
      // إنشاء ID للطلب
      const orderId = uuidv4();
      const orderWithId = {
        ...orderData,
        id: orderId,
        status: ORDER_STATUS.PENDING,
        timestamp: new Date().toISOString(),
      };

      console.log(
        "🔥 Adding new order:",
        orderId,
        "Table:",
        orderData.tableNumber
      );

      // إضافة الطلب إلى الحالة المحلية أولاً
      dispatch({
        type: ORDER_ACTIONS.ADD_ORDER,
        payload: orderWithId,
      });

      try {
        // حفظ في Supabase
        await dataService.saveOrder(orderWithId);
        console.log("✅ Order saved to database successfully");
      } catch (error) {
        console.error("❌ Error saving order to database:", error);
        // يمكن إضافة معالجة للخطأ هنا مثل إظهار رسالة للمستخدم
      }
    },

    updateOrderStatus: async (orderId, status, additionalData = {}) => {
      try {
        // Update in Supabase
        await dataService.updateOrderStatus(orderId, status, additionalData);
      } catch (error) {
        console.error("Error updating order status in database:", error);
      }

      dispatch({
        type: ORDER_ACTIONS.UPDATE_ORDER_STATUS,
        payload: { orderId, status, ...additionalData },
      });
    },

    waiterAcceptOrder: (orderId, waiterName) => {
      dispatch({
        type: ORDER_ACTIONS.WAITER_ACCEPT_ORDER,
        payload: { orderId, waiterName },
      });
    },

    waiterRejectOrder: (orderId, waiterName, reason) => {
      dispatch({
        type: ORDER_ACTIONS.WAITER_REJECT_ORDER,
        payload: { orderId, waiterName, reason },
      });
    },

    confirmPayment: (orderId, paymentMethod) => {
      dispatch({
        type: ORDER_ACTIONS.CONFIRM_PAYMENT,
        payload: { orderId, paymentMethod },
      });
    },

    cancelOrder: (orderId) => {
      dispatch({
        type: ORDER_ACTIONS.CANCEL_ORDER,
        payload: { orderId },
      });

      // Force immediate Supabase update for cancel action
      setTimeout(async () => {
        try {
          await dataService.updateOrderStatus(orderId, ORDER_STATUS.CANCELLED, {
            cancelledAt: new Date().toISOString(),
          });
        } catch (error) {
          console.error("Error updating cancelled order in Supabase:", error);
        }
      }, 50); // Very short delay to ensure state update
    },

    addToCart: (item, qty) => {
      dispatch({
        type: ORDER_ACTIONS.ADD_TO_CART,
        payload: { item, qty },
      });
    },

    updateCartQty: (itemId, qty) => {
      dispatch({
        type: ORDER_ACTIONS.UPDATE_CART_QTY,
        payload: { itemId, qty },
      });
    },

    removeFromCart: (itemId) => {
      dispatch({
        type: ORDER_ACTIONS.REMOVE_FROM_CART,
        payload: { itemId },
      });
    },

    clearCart: () => {
      dispatch({
        type: ORDER_ACTIONS.CLEAR_CART,
      });
    },

    setTableNumber: (tableNumber) => {
      dispatch({
        type: ORDER_ACTIONS.SET_TABLE_NUMBER,
        payload: { tableNumber },
      });
    },
  };

  return (
    <OrderContext.Provider value={{ state, actions }}>
      {children}
    </OrderContext.Provider>
  );
}

// Custom hook to use the order context
export function useOrder() {
  const context = useContext(OrderContext);
  if (!context) {
    throw new Error("useOrder must be used within an OrderProvider");
  }
  return context;
}
