import React, { useState, useEffect, useMemo } from "react";
import { useOrder, ORDER_STATUS } from "../context/OrderContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTable,
  faClock,
  faMoneyBillWave,
  faCheck,
  faEye,
  faSignInAlt,
  faChartLine,
  faBell,
  faCreditCard,
  faCoins,
  faClipboardList,
  faHandPointer,
  faMapMarkerAlt,
  faRefresh,
  faTimes,
  faExclamationTriangle,
  faThumbsUp,
  faThumbsDown,
  faInfoCircle,
} from "@fortawesome/free-solid-svg-icons";
import AvieLogo from "../components/AvieLogo";

const Waiter = () => {
  const { state, actions } = useOrder();
  const [currentWaiter, setCurrentWaiter] = useState(null);
  const [waiterName, setWaiterName] = useState("");
  const [showLoginModal, setShowLoginModal] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [waiterSales, setWaiterSales] = useState([]);
  const [todaySales, setTodaySales] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("cash");
  const [activeTab, setActiveTab] = useState("ready"); // ready, available
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showNewOrderModal, setShowNewOrderModal] = useState(false);
  const [newOrder, setNewOrder] = useState({
    tableNumber: "",
    customerName: "",
    items: [],
    notes: "",
  });
  const [availableProducts, setAvailableProducts] = useState([]);
  const [forceUpdate, setForceUpdate] = useState(0);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [orderToReject, setOrderToReject] = useState(null);

  // Load waiter data from localStorage
  useEffect(() => {
    const savedWaiter = localStorage.getItem("currentWaiter");
    if (savedWaiter) {
      const waiterData = JSON.parse(savedWaiter);
      setCurrentWaiter(waiterData);
      setShowLoginModal(false);
      loadWaiterSales(waiterData.name);
    }

    // Load available products
    loadAvailableProducts();
  }, []);

  // Load available products for manual order creation
  const loadAvailableProducts = () => {
    // تحميل المنتجات من localStorage أو استخدام البيانات الافتراضية
    const savedProducts = localStorage.getItem("menuData");
    let menuData;

    if (savedProducts) {
      menuData = JSON.parse(savedProducts);
    } else {
      // استيراد البيانات الافتراضية
      import("../data/menuData.js")
        .then((module) => {
          menuData = module.menuData;
          processMenuData(menuData);
        })
        .catch((error) => {
          console.error("خطأ في تحميل المنتجات:", error);
          setDefaultProducts();
        });
      return;
    }

    processMenuData(menuData);
  };

  const processMenuData = (menuData) => {
    const products = [];
    // تجميع جميع المنتجات من جميع الفئات
    menuData.forEach((category) => {
      if (category.items) {
        products.push(...category.items);
      }
    });
    setAvailableProducts(products);
  };

  const setDefaultProducts = () => {
    setAvailableProducts([
      { id: 1, name: "قهوة عربية", price: 15, desc: "قهوة عربية أصيلة" },
      { id: 2, name: "شاي أحمر", price: 10, desc: "شاي أحمر طازج" },
      { id: 3, name: "كرواسون", price: 12, desc: "كرواسون زبدة طازج" },
      { id: 4, name: "كيك شوكولاتة", price: 20, desc: "كيك شوكولاتة لذيذ" },
    ]);
  };

  // Load waiter sales
  const loadWaiterSales = (waiterName) => {
    const allSales = JSON.parse(localStorage.getItem("waiterSales") || "[]");
    const waiterSalesData = allSales.filter(
      (sale) => sale.waiterName === waiterName
    );
    setWaiterSales(waiterSalesData);

    // Calculate today's sales
    const today = new Date().toDateString();
    const todaysSales = waiterSalesData.filter(
      (sale) => new Date(sale.timestamp).toDateString() === today
    );
    const totalToday = todaysSales.reduce((sum, sale) => sum + sale.amount, 0);
    setTodaySales(totalToday);
  };

  // Waiter login
  const handleWaiterLogin = () => {
    if (!waiterName.trim()) {
      alert("يرجى إدخال اسم النادل");
      return;
    }

    const waiterData = {
      name: waiterName,
      loginTime: new Date().toISOString(),
      id: Date.now(),
    };

    setCurrentWaiter(waiterData);
    localStorage.setItem("currentWaiter", JSON.stringify(waiterData));
    setShowLoginModal(false);
    loadWaiterSales(waiterName);
  };

  // Waiter logout

  // Get ready orders for waiter (exclude cancelled orders)
  const getReadyOrders = () => {
    return state.orders.filter((order) => {
      const isReady = order.status === ORDER_STATUS.READY;
      const isAssigned = order.assignedWaiter === currentWaiter?.name;
      const isNotCancelled = order.status !== ORDER_STATUS.CANCELLED;
      return isReady && isAssigned && isNotCancelled;
    });
  };

  // Get new orders that need waiter response (includes all statuses except completed/cancelled)
  const getNewOrders = () => {
    return state.orders.filter((order) => {
      const isNotCompleted = order.status !== ORDER_STATUS.COMPLETED;
      const isNotCancelled = order.status !== ORDER_STATUS.CANCELLED;
      const isNotWaiterAccepted = order.status !== ORDER_STATUS.WAITER_ACCEPTED;
      const isNotWaiterRejected = order.status !== ORDER_STATUS.WAITER_REJECTED;
      const isNotAssignedToOtherWaiter =
        !order.assignedWaiter || order.assignedWaiter === currentWaiter?.name;

      return (
        isNotCompleted &&
        isNotCancelled &&
        isNotWaiterAccepted &&
        isNotWaiterRejected &&
        isNotAssignedToOtherWaiter
      );
    });
  };

  // Get available orders (ready and not assigned to any waiter, exclude cancelled)
  const getAvailableOrders = () => {
    return state.orders.filter((order) => {
      const isReady = order.status === ORDER_STATUS.READY;
      const isNotCancelled = order.status !== ORDER_STATUS.CANCELLED;
      const isNotAssigned = !order.assignedWaiter;
      const isNotPaid = !order.isPaid;
      return isReady && isNotCancelled && isNotAssigned && isNotPaid;
    });
  };

  // Claim an order for delivery
  const handleClaimOrder = (order) => {
    const updatedOrder = {
      ...order,
      assignedWaiter: currentWaiter.name,
      claimedAt: new Date().toISOString(),
      status: ORDER_STATUS.READY,
    };

    // Update order in context
    actions.updateOrderStatus(order.id, ORDER_STATUS.READY);

    // Save assignment in localStorage
    const orders = JSON.parse(localStorage.getItem("cafeOrders") || "[]");
    const updatedOrders = orders.map((o) =>
      o.id === order.id ? updatedOrder : o
    );
    localStorage.setItem("cafeOrders", JSON.stringify(updatedOrders));

    alert(`تم تخصيص الطلب رقم ${order.id.slice(-4)} لك بنجاح!`);

    // Switch to ready orders tab
    setActiveTab("ready");
  };

  // Calculate time elapsed since order creation
  const getTimeElapsed = (timestamp) => {
    const now = new Date();
    const orderTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - orderTime) / (1000 * 60));

    if (diffInMinutes < 1) return "الآن";
    if (diffInMinutes < 60) return `${diffInMinutes} دقيقة`;

    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} ساعة`;
  };

  // Close all modals
  const closeAllModals = () => {
    setShowPaymentModal(false);
    setSelectedOrder(null);
    setShowOrderDetails(false);
    setShowNewOrderModal(false);
  };

  // Add product to new order
  const addProductToOrder = (product) => {
    const existingItem = newOrder.items.find((item) => item.id === product.id);

    if (existingItem) {
      // زيادة الكمية إذا كان المنتج موجود
      setNewOrder((prev) => ({
        ...prev,
        items: prev.items.map((item) =>
          item.id === product.id ? { ...item, qty: item.qty + 1 } : item
        ),
      }));
    } else {
      // إضافة منتج جديد
      setNewOrder((prev) => ({
        ...prev,
        items: [...prev.items, { ...product, qty: 1 }],
      }));
    }
  };

  // Remove product from new order
  const removeProductFromOrder = (productId) => {
    setNewOrder((prev) => ({
      ...prev,
      items: prev.items.filter((item) => item.id !== productId),
    }));
  };

  // Update product quantity in new order
  const updateProductQuantity = (productId, newQty) => {
    if (newQty <= 0) {
      removeProductFromOrder(productId);
      return;
    }

    setNewOrder((prev) => ({
      ...prev,
      items: prev.items.map((item) =>
        item.id === productId ? { ...item, qty: newQty } : item
      ),
    }));
  };

  // Calculate order totals
  const calculateOrderTotals = () => {
    const subtotal = newOrder.items.reduce(
      (sum, item) => sum + item.price * item.qty,
      0
    );
    const tax = subtotal * 0.15; // 15% ضريبة
    const total = subtotal + tax;

    return { subtotal, tax, total };
  };

  // Submit new order
  const submitNewOrder = () => {
    if (!newOrder.tableNumber || newOrder.items.length === 0) {
      alert("يرجى إدخال رقم الطاولة واختيار منتج واحد على الأقل");
      return;
    }

    const { subtotal, tax, total } = calculateOrderTotals();

    const orderData = {
      id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      tableNumber: parseInt(newOrder.tableNumber),
      customerName:
        newOrder.customerName || `زبون طاولة ${newOrder.tableNumber}`,
      items: newOrder.items,
      notes: newOrder.notes,
      subtotal,
      tax,
      total,
      timestamp: new Date().toISOString(),
      status: "pending",
      createdBy: "waiter",
      waiterName: currentWaiter.name,
    };

    // إضافة الطلب إلى النظام
    actions.addOrder(orderData);

    // إعادة تعيين النموذج
    setNewOrder({
      tableNumber: "",
      customerName: "",
      items: [],
      notes: "",
    });

    closeAllModals();
    alert(`تم إضافة الطلب للطاولة ${orderData.tableNumber} بنجاح!`);
  };

  // Accept order function
  const handleAcceptOrder = (order) => {
    if (window.confirm(`هل تريد قبول طلب الطاولة ${order.tableNumber}؟`)) {
      actions.waiterAcceptOrder(order.id, currentWaiter.name);
      setForceUpdate((prev) => prev + 1);
      alert(`تم قبول طلب الطاولة ${order.tableNumber} بنجاح`);
    }
  };

  // Reject order function
  const handleRejectOrder = (order) => {
    setOrderToReject(order);
    setShowRejectionModal(true);
  };

  // Submit rejection
  const submitRejection = () => {
    if (!rejectionReason.trim()) {
      alert("يرجى إدخال سبب الرفض");
      return;
    }

    actions.waiterRejectOrder(
      orderToReject.id,
      currentWaiter.name,
      rejectionReason
    );
    setForceUpdate((prev) => prev + 1);
    setOrderToReject(null);
    setRejectionReason("");
    alert(`تم رفض طلب الطاولة ${orderToReject.tableNumber} بنجاح`);
  };

  // Cancel order function
  const handleCancelOrder = (order) => {
    if (
      window.confirm(`هل أنت متأكد من إلغاء طلب الطاولة ${order.tableNumber}؟`)
    ) {
      // Cancel the order through context
      actions.cancelOrder(order.id);

      // Close any open modals
      closeAllModals();

      // Force component re-render to update the lists
      setForceUpdate((prev) => prev + 1);

      alert(`تم إلغاء طلب الطاولة ${order.tableNumber} بنجاح`);
    }
  };

  // Open payment modal
  const handlePaymentClick = (order) => {
    // Close any existing modals first
    closeAllModals();

    // Then open payment modal
    setTimeout(() => {
      setSelectedOrder(order);
      setShowPaymentModal(true);
      setSelectedPaymentMethod("cash"); // Reset to default
    }, 100);
  };

  // Handle payment and record sale for waiter
  const confirmPayment = () => {
    if (!selectedOrder) return;

    // Confirm payment in the system
    actions.confirmPayment(selectedOrder.id, selectedPaymentMethod);

    // Record sale for waiter
    const saleData = {
      id: Date.now(),
      orderId: selectedOrder.id,
      waiterName: currentWaiter.name,
      tableNumber: selectedOrder.tableNumber,
      amount: selectedOrder.total,
      timestamp: new Date().toISOString(),
      paymentMethod: selectedPaymentMethod,
      customerName: selectedOrder.customerName || "",
    };

    // Save to waiter sales
    const allSales = JSON.parse(localStorage.getItem("waiterSales") || "[]");
    allSales.push(saleData);
    localStorage.setItem("waiterSales", JSON.stringify(allSales));

    // Update local state
    setWaiterSales((prev) => [...prev, saleData]);
    setTodaySales((prev) => prev + selectedOrder.total);

    // Close modal and show success message
    const paymentMethodText =
      selectedPaymentMethod === "cash" ? "نقداً" : "بالبطاقة";
    const tableNumber = selectedOrder.tableNumber;
    const total = selectedOrder.total;

    closeAllModals();

    alert(
      `تم تأكيد الدفع للطاولة ${tableNumber} بمبلغ ${total.toFixed(
        2
      )} ر.س ${paymentMethodText}`
    );
  };

  // Calculate time since order was ready
  const getTimeSinceReady = (order) => {
    if (!order.updatedAt) return "";
    const now = new Date();
    const readyTime = new Date(order.updatedAt);
    const diffInMinutes = Math.floor((now - readyTime) / (1000 * 60));

    if (diffInMinutes < 1) return "الآن";
    if (diffInMinutes < 60) return `${diffInMinutes} دقيقة`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} ساعة`;
  };

  // Get order status display
  const getOrderStatusDisplay = (order) => {
    switch (order.status) {
      case ORDER_STATUS.PENDING:
        return { text: "طلب جديد", color: "#FF9800", bgColor: "#FFF3E0" };
      case ORDER_STATUS.CONFIRMED:
        return {
          text: "مؤكد من الباريستا",
          color: "#2196F3",
          bgColor: "#E3F2FD",
        };
      case ORDER_STATUS.PREPARING:
        return { text: "قيد التحضير", color: "#9C27B0", bgColor: "#F3E5F5" };
      case ORDER_STATUS.READY:
        return { text: "جاهز للتسليم", color: "#4CAF50", bgColor: "#E8F5E8" };
      default:
        return { text: "طلب جديد", color: "#FF9800", bgColor: "#FFF3E0" };
    }
  };

  // Use forceUpdate to trigger re-render when orders are cancelled
  const newOrders = useMemo(() => getNewOrders(), [state.orders, forceUpdate]);
  const readyOrders = useMemo(
    () => getReadyOrders(),
    [state.orders, currentWaiter, forceUpdate]
  );
  const availableOrders = useMemo(
    () => getAvailableOrders(),
    [state.orders, forceUpdate]
  );

  if (showLoginModal) {
    return (
      <div className="waiter-login-container">
        <div className="waiter-login-modal">
          <div className="login-header">
            <AvieLogo size={80} />
            <h2>تسجيل دخول النادل</h2>
          </div>

          <div className="login-form">
            <div className="form-group">
              <label>اسم النادل:</label>
              <input
                type="text"
                value={waiterName}
                onChange={(e) => setWaiterName(e.target.value)}
                placeholder="أدخل اسمك"
                autoFocus
                onKeyPress={(e) => e.key === "Enter" && handleWaiterLogin()}
              />
            </div>

            <button className="login-btn" onClick={handleWaiterLogin}>
              <FontAwesomeIcon icon={faSignInAlt} />
              تسجيل الدخول
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="waiter-container">
      <div className="waiter-header">
        <div className="waiter-info">
          <p>
            مبيعات اليوم: <strong>{todaySales.toFixed(2)} ر.س</strong>
          </p>
        </div>

        <div className="waiter-actions">
          <div className="sales-summary">
            <FontAwesomeIcon icon={faChartLine} />
            <span>إجمالي المبيعات: {waiterSales.length} عملية</span>
          </div>
          <button
            className="add-order-btn"
            onClick={() => setShowNewOrderModal(true)}
          >
            <FontAwesomeIcon icon={faClipboardList} />
            إضافة طلب جديد
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="waiter-tabs">
        <button
          className={`tab-btn ${activeTab === "new" ? "active" : ""}`}
          onClick={() => setActiveTab("new")}
        >
          <FontAwesomeIcon icon={faExclamationTriangle} />
          طلبات جديدة ({newOrders.length})
        </button>
        <button
          className={`tab-btn ${activeTab === "ready" ? "active" : ""}`}
          onClick={() => setActiveTab("ready")}
        >
          <FontAwesomeIcon icon={faBell} />
          الطلبات الجاهزة ({readyOrders.length})
        </button>
        <button
          className={`tab-btn ${activeTab === "available" ? "active" : ""}`}
          onClick={() => setActiveTab("available")}
        >
          <FontAwesomeIcon icon={faClipboardList} />
          الطلبات المتاحة ({availableOrders.length})
        </button>
      </div>

      {/* New Orders Tab */}
      {activeTab === "new" && (
        <div className="new-orders-section">
          <h2>
            <FontAwesomeIcon icon={faExclamationTriangle} />
            طلبات جديدة تحتاج موافقة ({newOrders.length})
          </h2>

          {newOrders.length === 0 ? (
            <div className="no-orders">
              <FontAwesomeIcon icon={faExclamationTriangle} size="3x" />
              <p>لا توجد طلبات جديدة حالياً</p>
            </div>
          ) : (
            <div className="new-orders-grid">
              {newOrders.map((order) => (
                <div key={order.id} className="new-order-card">
                  <div className="order-header">
                    <div className="table-info">
                      <FontAwesomeIcon icon={faTable} />
                      <h3>طاولة {order.tableNumber}</h3>
                    </div>
                    <div className="order-time">
                      <FontAwesomeIcon icon={faClock} />
                      <span>منذ {getTimeElapsed(order.timestamp)}</span>
                    </div>
                  </div>

                  {/* Order Status Display */}
                  <div
                    className="order-status-badge"
                    style={{
                      backgroundColor: getOrderStatusDisplay(order).bgColor,
                      color: getOrderStatusDisplay(order).color,
                      padding: "0.5rem 1rem",
                      borderRadius: "20px",
                      textAlign: "center",
                      fontWeight: "600",
                      fontSize: "0.9rem",
                      margin: "1rem 0",
                      border: `2px solid ${getOrderStatusDisplay(order).color}`,
                    }}
                  >
                    <FontAwesomeIcon
                      icon={faInfoCircle}
                      style={{ marginLeft: "0.5rem" }}
                    />
                    {getOrderStatusDisplay(order).text}
                  </div>

                  <div className="order-summary">
                    <div className="customer-info">
                      {order.customerName && (
                        <p>
                          <strong>العميل:</strong> {order.customerName}
                        </p>
                      )}
                      <div className="items-preview">
                        <p>
                          <strong>الأصناف:</strong>
                        </p>
                        {order.items.slice(0, 2).map((item, index) => (
                          <span key={index} className="item-tag">
                            {item.qty}x {item.name}
                          </span>
                        ))}
                        {order.items.length > 2 && (
                          <span className="more-items">
                            +{order.items.length - 2} المزيد
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="order-total">
                      <FontAwesomeIcon icon={faMoneyBillWave} />
                      <span>{order.total.toFixed(2)} ر.س</span>
                    </div>
                  </div>

                  <div className="order-actions">
                    <button
                      className="view-details-btn"
                      onClick={() => {
                        closeAllModals();
                        setTimeout(() => setSelectedOrder(order), 100);
                      }}
                    >
                      <FontAwesomeIcon icon={faEye} />
                      عرض التفاصيل
                    </button>

                    {/* Show different buttons based on order status */}
                    {order.status === ORDER_STATUS.PENDING && (
                      <>
                        <button
                          className="accept-order-btn"
                          onClick={() => handleAcceptOrder(order)}
                        >
                          <FontAwesomeIcon icon={faThumbsUp} />
                          قبول الطلب
                        </button>

                        <button
                          className="reject-order-btn"
                          onClick={() => handleRejectOrder(order)}
                        >
                          <FontAwesomeIcon icon={faThumbsDown} />
                          رفض الطلب
                        </button>
                      </>
                    )}

                    {order.status === ORDER_STATUS.READY && (
                      <button
                        className="collect-payment-btn"
                        onClick={() => handlePaymentClick(order)}
                      >
                        <FontAwesomeIcon icon={faCheck} />
                        تحصيل المبلغ
                      </button>
                    )}

                    {(order.status === ORDER_STATUS.CONFIRMED ||
                      order.status === ORDER_STATUS.PREPARING) && (
                      <button
                        className="waiting-btn"
                        disabled
                        style={{
                          background: "#6c757d",
                          color: "white",
                          cursor: "not-allowed",
                          opacity: 0.7,
                        }}
                      >
                        <FontAwesomeIcon icon={faClock} />
                        في انتظار التحضير
                      </button>
                    )}

                    {/* Cancel button for all statuses except completed */}
                    {order.status !== ORDER_STATUS.COMPLETED && (
                      <button
                        className="cancel-order-btn"
                        onClick={() => handleCancelOrder(order)}
                      >
                        <FontAwesomeIcon icon={faTimes} />
                        إلغاء الطلب
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Ready Orders Tab */}
      {activeTab === "ready" && (
        <div className="ready-orders-section">
          <h2>
            <FontAwesomeIcon icon={faBell} />
            الطلبات الجاهزة للتسليم ({readyOrders.length})
          </h2>

          {readyOrders.length === 0 ? (
            <div className="no-orders">
              <FontAwesomeIcon icon={faTable} size="3x" />
              <p>لا توجد طلبات جاهزة حالياً</p>
            </div>
          ) : (
            <div className="ready-orders-grid">
              {readyOrders.map((order) => (
                <div key={order.id} className="ready-order-card">
                  <div className="order-header">
                    <div className="table-info">
                      <FontAwesomeIcon icon={faTable} />
                      <h3>طاولة {order.tableNumber}</h3>
                    </div>
                    <div className="ready-time">
                      <FontAwesomeIcon icon={faClock} />
                      <span>جاهز منذ {getTimeSinceReady(order)}</span>
                    </div>
                  </div>

                  <div className="order-summary">
                    <div className="customer-info">
                      {order.customerName && (
                        <p>
                          <strong>العميل:</strong> {order.customerName}
                        </p>
                      )}
                      <div className="items-preview">
                        <p>
                          <strong>الأصناف:</strong>
                        </p>
                        {order.items && order.items.length > 0 ? (
                          order.items.slice(0, 2).map((item, index) => (
                            <span key={index} className="item-tag">
                              {item.qty || item.quantity || 1}x{" "}
                              {item.name || "منتج غير محدد"}
                            </span>
                          ))
                        ) : (
                          <span className="no-items">لا توجد أصناف</span>
                        )}
                        {order.items.length > 2 && (
                          <span className="more-items">
                            +{order.items.length - 2} المزيد
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="order-total">
                      <FontAwesomeIcon icon={faMoneyBillWave} />
                      <span>{order.total.toFixed(2)} ر.س</span>
                    </div>
                  </div>

                  <div className="order-actions">
                    <button
                      className="view-details-btn"
                      onClick={() => {
                        closeAllModals();
                        setTimeout(() => setSelectedOrder(order), 100);
                      }}
                    >
                      <FontAwesomeIcon icon={faEye} />
                      عرض التفاصيل
                    </button>

                    <button
                      className="collect-payment-btn"
                      onClick={() => handlePaymentClick(order)}
                    >
                      <FontAwesomeIcon icon={faCheck} />
                      تحصيل المبلغ
                    </button>

                    <button
                      className="cancel-order-btn"
                      onClick={() => handleCancelOrder(order)}
                    >
                      <FontAwesomeIcon icon={faTimes} />
                      إلغاء الطلب
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Available Orders Tab */}
      {activeTab === "available" && (
        <div className="available-orders-section">
          <div className="section-header">
            <h2>
              <FontAwesomeIcon icon={faClipboardList} />
              الطلبات المتاحة للاختيار ({availableOrders.length})
            </h2>
            <div className="section-actions">
              <div className="stats-summary">
                <span className="stat-item">
                  <strong>{availableOrders.length}</strong> طلب متاح
                </span>
                <span className="stat-item">
                  <strong>
                    {availableOrders
                      .reduce((sum, order) => sum + order.total, 0)
                      .toFixed(2)}
                  </strong>{" "}
                  ر.س إجمالي
                </span>
              </div>
              <button
                className="refresh-btn"
                onClick={() => window.location.reload()}
              >
                <FontAwesomeIcon icon={faRefresh} />
                تحديث
              </button>
            </div>
          </div>

          {availableOrders.length === 0 ? (
            <div className="no-orders">
              <FontAwesomeIcon icon={faClipboardList} size="3x" />
              <h3>لا توجد طلبات متاحة حالياً</h3>
              <p>سيتم عرض الطلبات الجاهزة هنا عندما تصبح متاحة</p>
              <button
                className="refresh-btn"
                onClick={() => window.location.reload()}
              >
                <FontAwesomeIcon icon={faRefresh} />
                تحديث الصفحة
              </button>
            </div>
          ) : (
            <div className="available-orders-grid">
              {availableOrders.map((order) => (
                <div key={order.id} className="available-order-card">
                  <div className="order-header">
                    <div className="order-number">
                      <span>طلب #{order.id.slice(-4)}</span>
                      <span className="time-elapsed">
                        <FontAwesomeIcon icon={faClock} />
                        {getTimeElapsed(order.timestamp)}
                      </span>
                    </div>
                    <div className="table-info">
                      <FontAwesomeIcon icon={faMapMarkerAlt} />
                      <span>طاولة {order.tableNumber}</span>
                    </div>
                  </div>

                  <div className="order-content">
                    <div className="customer-info">
                      {order.customerName && (
                        <p>
                          <strong>العميل:</strong>
                          <span>{order.customerName}</span>
                        </p>
                      )}
                      <p>
                        <strong>عدد الأصناف:</strong>
                        <span>{order.items.length} صنف</span>
                      </p>
                      <p>
                        <strong>وقت الطلب:</strong>
                        <span>
                          {new Date(order.timestamp).toLocaleTimeString(
                            "ar-SA"
                          )}
                        </span>
                      </p>
                    </div>

                    <div className="order-summary">
                      <div className="total-amount">
                        <FontAwesomeIcon icon={faMoneyBillWave} />
                        <span>المبلغ: {order.total.toFixed(2)} ر.س</span>
                      </div>

                      <div className="items-preview">
                        <h4>الأصناف المطلوبة:</h4>
                        <div className="items-list">
                          {order.items && order.items.length > 0 ? (
                            order.items.slice(0, 3).map((item, index) => (
                              <span key={index} className="item-tag">
                                {item.qty || item.quantity || 1}x{" "}
                                {item.name || "منتج غير محدد"}
                              </span>
                            ))
                          ) : (
                            <span className="no-items">لا توجد أصناف</span>
                          )}
                          {order.items.length > 3 && (
                            <span className="more-items">
                              +{order.items.length - 3} المزيد
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {order.notes && (
                      <div className="order-notes">
                        <strong>ملاحظات:</strong> {order.notes}
                      </div>
                    )}
                  </div>

                  <div className="order-actions">
                    <button
                      className="view-btn"
                      onClick={() => {
                        closeAllModals();
                        setTimeout(() => {
                          setSelectedOrder(order);
                          setShowOrderDetails(true);
                        }, 100);
                      }}
                    >
                      <FontAwesomeIcon icon={faEye} />
                      عرض التفاصيل
                    </button>
                    <button
                      className="claim-btn"
                      onClick={() => handleClaimOrder(order)}
                    >
                      <FontAwesomeIcon icon={faHandPointer} />
                      اختيار هذا الطلب
                    </button>
                    <button
                      className="cancel-order-btn"
                      onClick={() => handleCancelOrder(order)}
                    >
                      <FontAwesomeIcon icon={faTimes} />
                      إلغاء الطلب
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="waiter-stats">
        <h3>إحصائيات اليوم</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <FontAwesomeIcon icon={faMoneyBillWave} />
            <div>
              <span>إجمالي المبيعات</span>
              <strong>{todaySales.toFixed(2)} ر.س</strong>
            </div>
          </div>

          <div className="stat-item">
            <FontAwesomeIcon icon={faTable} />
            <div>
              <span>عدد الطلبات</span>
              <strong>
                {
                  waiterSales.filter(
                    (sale) =>
                      new Date(sale.timestamp).toDateString() ===
                      new Date().toDateString()
                  ).length
                }
              </strong>
            </div>
          </div>

          <div className="stat-item">
            <FontAwesomeIcon icon={faClock} />
            <div>
              <span>وقت العمل</span>
              <strong>
                {Math.floor(
                  (new Date() - new Date(currentWaiter.loginTime)) /
                    (1000 * 60 * 60)
                )}{" "}
                ساعة
              </strong>
            </div>
          </div>
        </div>
      </div>

      {/* Order Details Modal for Available Orders */}
      {showOrderDetails && selectedOrder && (
        <div className="order-modal-overlay" onClick={closeAllModals}>
          <div className="order-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>تفاصيل الطلب #{selectedOrder.id.slice(-4)}</h2>
              <button className="close-btn" onClick={closeAllModals}>
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="order-info-grid">
                <div className="info-item">
                  <strong>رقم الطاولة:</strong>
                  <span>{selectedOrder.tableNumber}</span>
                </div>
                <div className="info-item">
                  <strong>وقت الطلب:</strong>
                  <span>
                    {new Date(selectedOrder.timestamp).toLocaleString("ar-SA")}
                  </span>
                </div>
                {selectedOrder.customerName && (
                  <div className="info-item">
                    <strong>اسم العميل:</strong>
                    <span>{selectedOrder.customerName}</span>
                  </div>
                )}
              </div>

              <div className="items-list">
                <h3>الأصناف المطلوبة:</h3>
                {selectedOrder.items && selectedOrder.items.length > 0 ? (
                  selectedOrder.items.map((item, index) => (
                    <div key={index} className="item-row">
                      <span className="item-qty">
                        {item.qty || item.quantity || 1}x
                      </span>
                      <span className="item-name">
                        {item.name || "منتج غير محدد"}
                      </span>
                      <span className="item-price">
                        {(
                          (item.qty || item.quantity || 1) * (item.price || 0)
                        ).toFixed(2)}{" "}
                        ر.س
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="no-items-message">
                    <p>لا توجد أصناف في هذا الطلب</p>
                  </div>
                )}
              </div>

              <div className="order-totals">
                <div className="total-row">
                  <span>المجموع الفرعي:</span>
                  <span>{selectedOrder.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div className="total-row">
                  <span>الضريبة (15%):</span>
                  <span>{selectedOrder.tax.toFixed(2)} ر.س</span>
                </div>
                <div className="total-row final">
                  <span>الإجمالي:</span>
                  <span>{selectedOrder.total.toFixed(2)} ر.س</span>
                </div>
              </div>

              {selectedOrder.notes && (
                <div className="notes-section">
                  <h3>ملاحظات خاصة:</h3>
                  <p>{selectedOrder.notes}</p>
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="claim-btn"
                onClick={() => {
                  handleClaimOrder(selectedOrder);
                  closeAllModals();
                }}
              >
                <FontAwesomeIcon icon={faHandPointer} />
                اختيار هذا الطلب
              </button>
              <button className="cancel-btn" onClick={closeAllModals}>
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {selectedOrder && !showPaymentModal && !showOrderDetails && (
        <div className="order-modal-overlay" onClick={closeAllModals}>
          <div className="order-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>تفاصيل الطلب - طاولة {selectedOrder.tableNumber}</h2>
              <button className="close-btn" onClick={closeAllModals}>
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="order-details">
                <p>
                  <strong>وقت الطلب:</strong>{" "}
                  {new Date(selectedOrder.timestamp).toLocaleString("ar-SA")}
                </p>
                {selectedOrder.customerName && (
                  <p>
                    <strong>اسم العميل:</strong> {selectedOrder.customerName}
                  </p>
                )}
                {selectedOrder.notes && (
                  <p>
                    <strong>ملاحظات:</strong> {selectedOrder.notes}
                  </p>
                )}
              </div>

              <div className="order-items-detail">
                <h3>الأصناف المطلوبة:</h3>
                {selectedOrder.items && selectedOrder.items.length > 0 ? (
                  selectedOrder.items.map((item, index) => (
                    <div key={index} className="item-detail">
                      <div className="item-info">
                        <span className="item-name">
                          {item.name || "منتج غير محدد"}
                        </span>
                        {item.desc && (
                          <span className="item-desc">{item.desc}</span>
                        )}
                      </div>
                      <div className="item-qty-price">
                        <span className="qty">
                          {item.qty || item.quantity || 1}x
                        </span>
                        <span className="price">
                          {(
                            (item.qty || item.quantity || 1) * (item.price || 0)
                          ).toFixed(2)}{" "}
                          ر.س
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-items-message">
                    <p>لا توجد أصناف في هذا الطلب</p>
                  </div>
                )}
              </div>

              <div className="order-summary">
                <div className="summary-row">
                  <span>المجموع الفرعي:</span>
                  <span>{selectedOrder.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div className="summary-row">
                  <span>الضريبة:</span>
                  <span>{selectedOrder.tax.toFixed(2)} ر.س</span>
                </div>
                <div className="summary-row total">
                  <span>الإجمالي:</span>
                  <span>{selectedOrder.total.toFixed(2)} ر.س</span>
                </div>
              </div>

              <div className="modal-actions">
                <button
                  className="collect-payment-btn"
                  onClick={() => {
                    setShowPaymentModal(true);
                  }}
                >
                  <FontAwesomeIcon icon={faCheck} />
                  تحصيل المبلغ
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showPaymentModal && selectedOrder && (
        <div
          className="payment-modal-overlay waiter-payment-modal"
          onClick={closeAllModals}
        >
          <div
            className="payment-modal waiter-payment-content"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header">
              <h2>تحصيل المبلغ - طاولة {selectedOrder.tableNumber}</h2>
              <button className="close-btn" onClick={closeAllModals}>
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="payment-summary">
                <h3>ملخص الطلب:</h3>
                <div className="summary-row">
                  <span>المجموع الفرعي:</span>
                  <span>{selectedOrder.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div className="summary-row">
                  <span>الضريبة (15%):</span>
                  <span>{selectedOrder.tax.toFixed(2)} ر.س</span>
                </div>
                <div className="summary-row total">
                  <span>الإجمالي:</span>
                  <span>{selectedOrder.total.toFixed(2)} ر.س</span>
                </div>
              </div>

              <div className="payment-method">
                <h3>طريقة الدفع:</h3>
                <div className="payment-options">
                  <label
                    className={
                      selectedPaymentMethod === "cash" ? "selected" : ""
                    }
                  >
                    <input
                      type="radio"
                      value="cash"
                      checked={selectedPaymentMethod === "cash"}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                    />
                    <div className="payment-option-content">
                      <FontAwesomeIcon icon={faCoins} />
                      <span>نقداً</span>
                    </div>
                  </label>
                  <label
                    className={
                      selectedPaymentMethod === "card" ? "selected" : ""
                    }
                  >
                    <input
                      type="radio"
                      value="card"
                      checked={selectedPaymentMethod === "card"}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                    />
                    <div className="payment-option-content">
                      <FontAwesomeIcon icon={faCreditCard} />
                      <span>بطاقة بنك</span>
                    </div>
                  </label>
                </div>
              </div>

              <div className="modal-actions">
                <button
                  className="confirm-payment-btn"
                  onClick={confirmPayment}
                >
                  <FontAwesomeIcon icon={faCheck} />
                  تأكيد الدفع
                </button>
                <button className="cancel-btn" onClick={closeAllModals}>
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* New Order Modal */}
      {showNewOrderModal && (
        <div className="order-modal-overlay" onClick={closeAllModals}>
          <div className="new-order-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>إضافة طلب جديد</h2>
              <button className="close-btn" onClick={closeAllModals}>
                ×
              </button>
            </div>

            <div className="modal-content">
              {/* معلومات الطلب الأساسية */}
              <div className="order-basic-info">
                <div className="form-group">
                  <label>رقم الطاولة *</label>
                  <input
                    type="number"
                    value={newOrder.tableNumber}
                    onChange={(e) =>
                      setNewOrder((prev) => ({
                        ...prev,
                        tableNumber: e.target.value,
                      }))
                    }
                    placeholder="أدخل رقم الطاولة"
                    min="1"
                    max="50"
                  />
                </div>

                <div className="form-group">
                  <label>اسم العميل (اختياري)</label>
                  <input
                    type="text"
                    value={newOrder.customerName}
                    onChange={(e) =>
                      setNewOrder((prev) => ({
                        ...prev,
                        customerName: e.target.value,
                      }))
                    }
                    placeholder="اسم العميل"
                  />
                </div>
              </div>

              {/* اختيار المنتجات */}
              <div className="products-selection">
                <h3>اختيار المنتجات</h3>
                <div className="products-grid">
                  {availableProducts.map((product) => (
                    <div key={product.id} className="product-card">
                      <div className="product-info">
                        <h4>{product.name}</h4>
                        <p>{product.desc}</p>
                        <span className="product-price">
                          {product.price.toFixed(2)} ر.س
                        </span>
                      </div>
                      <button
                        className="add-product-btn"
                        onClick={() => addProductToOrder(product)}
                      >
                        إضافة
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* المنتجات المختارة */}
              {newOrder.items.length > 0 && (
                <div className="selected-items">
                  <h3>المنتجات المختارة</h3>
                  {newOrder.items.map((item) => (
                    <div key={item.id} className="selected-item">
                      <div className="item-details">
                        <span className="item-name">{item.name}</span>
                        <span className="item-price">
                          {item.price.toFixed(2)} ر.س
                        </span>
                      </div>
                      <div className="quantity-controls">
                        <button
                          onClick={() =>
                            updateProductQuantity(item.id, item.qty - 1)
                          }
                          className="qty-btn"
                        >
                          -
                        </button>
                        <span className="quantity">{item.qty}</span>
                        <button
                          onClick={() =>
                            updateProductQuantity(item.id, item.qty + 1)
                          }
                          className="qty-btn"
                        >
                          +
                        </button>
                      </div>
                      <button
                        onClick={() => removeProductFromOrder(item.id)}
                        className="remove-item-btn"
                      >
                        حذف
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* ملاحظات */}
              <div className="form-group">
                <label>ملاحظات خاصة (اختياري)</label>
                <textarea
                  value={newOrder.notes}
                  onChange={(e) =>
                    setNewOrder((prev) => ({ ...prev, notes: e.target.value }))
                  }
                  placeholder="أي ملاحظات خاصة للطلب..."
                  rows="3"
                />
              </div>

              {/* ملخص الطلب */}
              {newOrder.items.length > 0 && (
                <div className="order-summary-new">
                  <h3>ملخص الطلب</h3>
                  {(() => {
                    const { subtotal, tax, total } = calculateOrderTotals();
                    return (
                      <div className="totals">
                        <div className="total-row">
                          <span>المجموع الفرعي:</span>
                          <span>{subtotal.toFixed(2)} ر.س</span>
                        </div>
                        <div className="total-row">
                          <span>الضريبة (15%):</span>
                          <span>{tax.toFixed(2)} ر.س</span>
                        </div>
                        <div className="total-row final">
                          <span>الإجمالي:</span>
                          <span>{total.toFixed(2)} ر.س</span>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="submit-order-btn"
                onClick={submitNewOrder}
                disabled={!newOrder.tableNumber || newOrder.items.length === 0}
              >
                إضافة الطلب
              </button>
              <button className="cancel-btn" onClick={closeAllModals}>
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Waiter;
