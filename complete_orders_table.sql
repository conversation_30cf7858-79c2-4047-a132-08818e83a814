-- جدول الطلبات الشامل - يحتوي على جميع المعلومات المطلوبة
-- اسم المنتج + سعره + الضريبة + اسم العميل + الملاحظات + رقم الطاولة

-- حذف الجدول القديم إذا كان موجوداً (احذر: سيمحو جميع البيانات!)
-- DROP TABLE IF EXISTS "public"."complete_orders";

-- إنشاء الجدول الشامل الجديد
CREATE TABLE IF NOT EXISTS "public"."complete_orders" (
    -- معرف فريد لكل سجل
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- معلومات الطلب الأساسية
    "order_id" UUID NOT NULL,                           -- معر<PERSON> الطلب (يمكن أن يكون نفسه لعدة منتجات)
    "table_number" INTEGER NOT NULL,                    -- رقم الطاولة
    "customer_name" VARCHAR(255) DEFAULT '',            -- اسم العميل
    "notes" TEXT DEFAULT '',                            -- ملاحظات الطلب
    
    -- معلومات المنتج
    "product_id" UUID,                                  -- معرف المنتج (للربط مع جدول المنتجات)
    "product_name" VARCHAR(255) NOT NULL,              -- اسم المنتج
    "product_description" TEXT DEFAULT '',             -- وصف المنتج
    "unit_price" DECIMAL(10,2) NOT NULL DEFAULT 0.00,  -- سعر الوحدة
    "quantity" INTEGER NOT NULL DEFAULT 1,             -- الكمية
    "item_total" DECIMAL(10,2) NOT NULL DEFAULT 0.00,  -- إجمالي المنتج (سعر × كمية)
    
    -- معلومات الضريبة والمجاميع
    "tax_rate" DECIMAL(5,2) NOT NULL DEFAULT 15.00,    -- معدل الضريبة (%)
    "tax_amount" DECIMAL(10,2) NOT NULL DEFAULT 0.00,  -- قيمة الضريبة لهذا المنتج
    "total_with_tax" DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- المجموع مع الضريبة
    
    -- معلومات حالة الطلب
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',   -- حالة الطلب
    "assigned_waiter" VARCHAR(255),                    -- النادل المسؤول
    "waiter_id" UUID,                                  -- معرف النادل
    
    -- معلومات إضافية
    "twitter_handle" VARCHAR(100),                     -- حساب تويتر للعميل
    "payment_method" VARCHAR(50),                      -- طريقة الدفع
    "is_paid" BOOLEAN DEFAULT FALSE,                   -- هل تم الدفع؟
    
    -- طوابع زمنية
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS "idx_complete_orders_order_id" ON "public"."complete_orders" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_complete_orders_table_number" ON "public"."complete_orders" ("table_number");
CREATE INDEX IF NOT EXISTS "idx_complete_orders_status" ON "public"."complete_orders" ("status");
CREATE INDEX IF NOT EXISTS "idx_complete_orders_customer_name" ON "public"."complete_orders" ("customer_name");
CREATE INDEX IF NOT EXISTS "idx_complete_orders_product_name" ON "public"."complete_orders" ("product_name");
CREATE INDEX IF NOT EXISTS "idx_complete_orders_created_at" ON "public"."complete_orders" ("created_at");

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_complete_orders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_complete_orders_updated_at 
    BEFORE UPDATE ON "public"."complete_orders" 
    FOR EACH ROW 
    EXECUTE FUNCTION update_complete_orders_updated_at();

-- إنشاء trigger لحساب المجاميع تلقائياً
CREATE OR REPLACE FUNCTION calculate_complete_orders_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- حساب إجمالي المنتج
    NEW.item_total = NEW.unit_price * NEW.quantity;
    
    -- حساب قيمة الضريبة
    NEW.tax_amount = NEW.item_total * (NEW.tax_rate / 100);
    
    -- حساب المجموع مع الضريبة
    NEW.total_with_tax = NEW.item_total + NEW.tax_amount;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_complete_orders_totals 
    BEFORE INSERT OR UPDATE ON "public"."complete_orders" 
    FOR EACH ROW 
    EXECUTE FUNCTION calculate_complete_orders_totals();

-- إضافة قيود للتحقق من صحة البيانات
ALTER TABLE "public"."complete_orders" 
ADD CONSTRAINT IF NOT EXISTS "check_table_number_positive" 
CHECK ("table_number" > 0);

ALTER TABLE "public"."complete_orders" 
ADD CONSTRAINT IF NOT EXISTS "check_unit_price_non_negative" 
CHECK ("unit_price" >= 0);

ALTER TABLE "public"."complete_orders" 
ADD CONSTRAINT IF NOT EXISTS "check_quantity_positive" 
CHECK ("quantity" > 0);

ALTER TABLE "public"."complete_orders" 
ADD CONSTRAINT IF NOT EXISTS "check_tax_rate_valid" 
CHECK ("tax_rate" >= 0 AND "tax_rate" <= 100);

ALTER TABLE "public"."complete_orders" 
ADD CONSTRAINT IF NOT EXISTS "check_status_valid" 
CHECK ("status" IN ('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'));

-- تعليقات على الجدول والأعمدة
COMMENT ON TABLE "public"."complete_orders" IS 'جدول الطلبات الشامل - يحتوي على جميع معلومات الطلب والمنتجات';
COMMENT ON COLUMN "public"."complete_orders"."id" IS 'معرف فريد لكل سجل';
COMMENT ON COLUMN "public"."complete_orders"."order_id" IS 'معرف الطلب (نفس المعرف لجميع منتجات الطلب الواحد)';
COMMENT ON COLUMN "public"."complete_orders"."table_number" IS 'رقم الطاولة';
COMMENT ON COLUMN "public"."complete_orders"."customer_name" IS 'اسم العميل';
COMMENT ON COLUMN "public"."complete_orders"."notes" IS 'ملاحظات الطلب';
COMMENT ON COLUMN "public"."complete_orders"."product_name" IS 'اسم المنتج';
COMMENT ON COLUMN "public"."complete_orders"."unit_price" IS 'سعر الوحدة';
COMMENT ON COLUMN "public"."complete_orders"."quantity" IS 'الكمية';
COMMENT ON COLUMN "public"."complete_orders"."tax_rate" IS 'معدل الضريبة (%)';
COMMENT ON COLUMN "public"."complete_orders"."tax_amount" IS 'قيمة الضريبة (محسوبة تلقائياً)';
COMMENT ON COLUMN "public"."complete_orders"."total_with_tax" IS 'المجموع مع الضريبة (محسوب تلقائياً)';

-- إدراج بيانات تجريبية
INSERT INTO "public"."complete_orders" (
    "order_id",
    "table_number",
    "customer_name",
    "notes",
    "product_name",
    "product_description",
    "unit_price",
    "quantity",
    "status"
) VALUES 
-- طلب رقم 1 - طاولة 5 - أحمد محمد
('550e8400-e29b-41d4-a716-446655440001', 5, 'أحمد محمد', 'بدون سكر', 'قهوة تركية', 'قهوة بن عربي أصيلة مع الهيل', 15.00, 2, 'pending'),
('550e8400-e29b-41d4-a716-446655440001', 5, 'أحمد محمد', 'بدون سكر', 'كابتشينو', 'إسبريسو مع حليب مبخر ورغوة', 18.00, 1, 'pending'),

-- طلب رقم 2 - طاولة 3 - فاطمة علي
('550e8400-e29b-41d4-a716-446655440002', 3, 'فاطمة علي', 'سكر خفيف', 'لاتيه', 'إسبريسو مع حليب ساخن', 20.00, 1, 'confirmed'),
('550e8400-e29b-41d4-a716-446655440002', 3, 'فاطمة علي', 'سكر خفيف', 'كرواسون', 'كرواسون فرنسي طازج', 12.00, 2, 'confirmed'),

-- طلب رقم 3 - طاولة 7 - محمد سالم
('550e8400-e29b-41d4-a716-446655440003', 7, 'محمد سالم', 'بدون حليب', 'إسبريسو', 'قهوة إسبريسو قوية', 10.00, 3, 'preparing');

-- عرض النتائج
SELECT 
    order_id,
    table_number,
    customer_name,
    product_name,
    unit_price,
    quantity,
    tax_rate,
    tax_amount,
    total_with_tax,
    notes,
    status,
    created_at
FROM "public"."complete_orders" 
ORDER BY created_at DESC, order_id, product_name;

-- إحصائيات مفيدة
SELECT 
    'إجمالي الطلبات' as metric,
    COUNT(DISTINCT order_id) as value
FROM "public"."complete_orders"

UNION ALL

SELECT 
    'إجمالي المنتجات',
    COUNT(*) as value
FROM "public"."complete_orders"

UNION ALL

SELECT 
    'إجمالي المبيعات (مع الضريبة)',
    ROUND(SUM(total_with_tax), 2) as value
FROM "public"."complete_orders";

SELECT 'تم إنشاء جدول الطلبات الشامل بنجاح!' as message;
