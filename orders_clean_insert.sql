-- حل بسيط: حذف السجلات الموجودة وإدراج جديدة بأنواع البيانات الصحيحة

-- حذف السجلات الموجودة أولاً
DELETE FROM "public"."orders" 
WHERE "id" IN (
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4',
    'da750940-85e0-43f4-bfa4-d1858237e33c',
    'e7969884-95ea-4fba-a0f8-315b21986e5b'
);

-- إدراج السجلات بأنواع البيانات الصحيحة
INSERT INTO "public"."orders" (
    "id", 
    "table_number", 
    "customer_name", 
    "notes", 
    "twitter_handle", 
    "subtotal", 
    "tax", 
    "total", 
    "status", 
    "assigned_waiter", 
    "waiter_id", 
    "created_at", 
    "updated_at"
) VALUES 
(
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4', 
    1,                          -- ✅ INTEGER بدلاً من VARCHAR
    'زبون طاولة 1', 
    '', 
    null, 
    15.00,                      -- ✅ DECIMAL بدلاً من VARCHAR
    2.25,                       -- ✅ DECIMAL بدلاً من VARCHAR
    17.25,                      -- ✅ DECIMAL بدلاً من VARCHAR
    'cancelled', 
    null, 
    null, 
    '2025-06-16 17:36:09.854332+00', 
    '2025-06-16 20:34:53.062251+00'
),
(
    'da750940-85e0-43f4-bfa4-d1858237e33c', 
    2,                          -- ✅ INTEGER بدلاً من VARCHAR
    '', 
    '', 
    null, 
    32.00,                      -- ✅ DECIMAL بدلاً من VARCHAR
    4.80,                       -- ✅ DECIMAL بدلاً من VARCHAR
    36.80,                      -- ✅ DECIMAL بدلاً من VARCHAR
    'confirmed', 
    null, 
    null, 
    '2025-06-17 18:51:40.311111+00', 
    '2025-06-17 19:07:18.678453+00'
),
(
    'e7969884-95ea-4fba-a0f8-315b21986e5b', 
    1,                          -- ✅ INTEGER بدلاً من VARCHAR
    '', 
    '', 
    null, 
    70.00,                      -- ✅ DECIMAL بدلاً من VARCHAR
    10.50,                      -- ✅ DECIMAL بدلاً من VARCHAR
    80.50,                      -- ✅ DECIMAL بدلاً من VARCHAR
    'pending', 
    null, 
    null, 
    '2025-06-17 19:49:08.201501+00', 
    '2025-06-17 19:49:08.201501+00'
);

-- التحقق من النتيجة
SELECT 
    id,
    table_number,
    customer_name,
    subtotal,
    tax,
    total,
    status,
    created_at,
    -- التحقق من أنواع البيانات
    pg_typeof(table_number) as table_number_type,
    pg_typeof(subtotal) as subtotal_type,
    pg_typeof(tax) as tax_type,
    pg_typeof(total) as total_type
FROM "public"."orders" 
WHERE id IN (
    '6aa1ded8-12de-4cc1-9b60-1df22941f2f4',
    'da750940-85e0-43f4-bfa4-d1858237e33c',
    'e7969884-95ea-4fba-a0f8-315b21986e5b'
)
ORDER BY created_at DESC;

-- رسالة نجاح
SELECT 'تم إصلاح السجلات بنجاح! أنواع البيانات صحيحة الآن.' as message;
