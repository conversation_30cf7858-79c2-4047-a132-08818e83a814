import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faImage,
  faTimes,
  faCheck,
  faTrash,
  faEdit,
  faArrowUp,
  faArrowDown,
  faSort,
} from "@fortawesome/free-solid-svg-icons";

import { dataService, DataService } from "../services/dataService";

const Admin = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [activeTab, setActiveTab] = useState("products");
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showEditCategoryForm, setShowEditCategoryForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [editingCategory, setEditingCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: "",
    nameEn: "",
    price: "",
    image: "",
    category: "",
    desc: "",
    descEn: "",
  });
  const [newCategory, setNewCategory] = useState({
    name: "",
    nameEn: "",
    description: "",
    descriptionEn: "",
  });
  const [imagePreview, setImagePreview] = useState("");

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // تحميل المنتجات
        setLoadingProducts(true);
        const productsData = await dataService.getProducts();
        setProducts(productsData);
        setLoadingProducts(false);

        // تحميل الفئات
        setLoadingCategories(true);
        const categoriesData = await dataService.getCategories();
        console.log("Admin - Loaded categories:", categoriesData);

        // استخراج الفئات من المنتجات كبديل
        const productCategories = [
          ...new Set(productsData.map((item) => item.category)),
        ];

        if (categoriesData && categoriesData.length > 0) {
          // إزالة التكرار من الفئات
          const uniqueCategories = [
            ...new Map(
              categoriesData.map((item) => [item.name, item])
            ).values(),
          ];
          setCategories(uniqueCategories);

          // تعيين الفئة الافتراضية لأول منتج
          if (uniqueCategories.length > 0) {
            setNewProduct((prev) => ({
              ...prev,
              category: uniqueCategories[0].name,
            }));
          }
        } else {
          // إذا لم توجد فئات، استخرجها من المنتجات
          console.log("Admin - No categories found, extracting from products");
          const fallbackCategories = productCategories.map((name, index) => ({
            id: index + 1,
            name,
            name_en: DataService.getCategoryEnglishName(name),
            display_order: index + 1,
            is_active: true,
            description: "",
            description_en: "",
          }));
          setCategories(fallbackCategories);

          // تعيين الفئة الافتراضية لأول منتج
          if (fallbackCategories.length > 0) {
            setNewProduct((prev) => ({
              ...prev,
              category: fallbackCategories[0].name,
            }));
          }
        }
        setLoadingCategories(false);
      } catch (error) {
        console.error("Error loading data:", error);
        setProducts([]);
        setCategories([]);
        setLoadingProducts(false);
        setLoadingCategories(false);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleAddCategory = async (e) => {
    e.preventDefault();

    if (!newCategory.name) {
      alert("يرجى إدخال اسم الفئة");
      return;
    }

    try {
      const categoryData = {
        ...newCategory,
        display_order: categories.length + 1, // ترتيب جديد
      };

      await dataService.saveCategory(categoryData);
      const updatedCategories = await dataService.getCategories();
      console.log("Updated categories after add:", updatedCategories);

      if (updatedCategories && updatedCategories.length > 0) {
        // إزالة التكرار من الفئات
        const uniqueCategories = [
          ...new Map(
            updatedCategories.map((item) => [item.name, item])
          ).values(),
        ];
        setCategories(uniqueCategories);
      } else {
        // إعادة تحميل البيانات من جديد
        const productsData = await dataService.getProducts();
        const productCategories = [
          ...new Set(productsData.map((item) => item.category)),
        ];
        const fallbackCategories = productCategories.map((name, index) => ({
          id: index + 1,
          name,
          name_en: DataService.getCategoryEnglishName(name),
          display_order: index + 1,
          is_active: true,
          description: "",
          description_en: "",
        }));
        setCategories(fallbackCategories);
      }

      setNewCategory({
        name: "",
        nameEn: "",
        description: "",
        descriptionEn: "",
      });

      setShowCategoryForm(false);
      alert("تم إضافة الفئة بنجاح!");
    } catch (error) {
      console.error("Error adding category:", error);
      alert("حدث خطأ أثناء إضافة الفئة. يرجى المحاولة مرة أخرى.");
    }
  };

  const startEditCategory = (category) => {
    setEditingCategory(category);
    setNewCategory({
      name: category.name,
      nameEn: category.name_en || category.nameEn || "",
      description: category.description || "",
      descriptionEn: category.description_en || category.descriptionEn || "",
    });
    setShowEditCategoryForm(true);
  };

  const handleEditCategory = async (e) => {
    e.preventDefault();

    if (!newCategory.name) {
      alert("يرجى إدخال اسم الفئة");
      return;
    }

    try {
      const categoryData = {
        name: newCategory.name,
        name_en: newCategory.nameEn,
        description: newCategory.description,
        description_en: newCategory.descriptionEn,
        display_order: editingCategory.display_order || 0,
      };

      await dataService.updateCategory(editingCategory.id, categoryData);
      const updatedCategories = await dataService.getCategories();
      console.log("Updated categories after edit:", updatedCategories);

      if (updatedCategories && updatedCategories.length > 0) {
        // إزالة التكرار من الفئات
        const uniqueCategories = [
          ...new Map(
            updatedCategories.map((item) => [item.name, item])
          ).values(),
        ];
        setCategories(uniqueCategories);
      } else {
        // إعادة تحميل البيانات من جديد
        const productsData = await dataService.getProducts();
        const productCategories = [
          ...new Set(productsData.map((item) => item.category)),
        ];
        const fallbackCategories = productCategories.map((name, index) => ({
          id: index + 1,
          name,
          name_en: DataService.getCategoryEnglishName(name),
          display_order: index + 1,
          is_active: true,
          description: "",
          description_en: "",
        }));
        setCategories(fallbackCategories);
      }

      setNewCategory({
        name: "",
        nameEn: "",
        description: "",
        descriptionEn: "",
      });

      setShowEditCategoryForm(false);
      setEditingCategory(null);
      alert("تم تحديث الفئة بنجاح!");
    } catch (error) {
      console.error("Error updating category:", error);
      alert("حدث خطأ أثناء تحديث الفئة. يرجى المحاولة مرة أخرى.");
    }
  };

  const deleteCategory = async (categoryId, categoryName) => {
    // التحقق من وجود منتجات في هذه الفئة
    const hasProducts = products.some(
      (category) =>
        category.category === categoryName &&
        category.items &&
        category.items.length > 0
    );

    // عد المنتجات في الفئة
    const productsCount = hasProducts
      ? products.find((cat) => cat.category === categoryName)?.items?.length ||
        0
      : 0;

    let confirmMessage = `هل أنت متأكد من حذف فئة "${categoryName}"؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء.`;

    if (hasProducts) {
      confirmMessage = `⚠️ تحذير: فئة "${categoryName}" تحتوي على ${productsCount} منتج!\n\nاختر أحد الخيارات التالية:\n\n1️⃣ اضغط "موافق" لحذف الفئة فقط (سيتم الاحتفاظ بالمنتجات)\n2️⃣ اضغط "إلغاء" ثم استخدم زر "حذف مع المنتجات" لحذف الفئة والمنتجات معاً\n\nلا يمكن التراجع عن هذا الإجراء.`;
    }

    if (window.confirm(confirmMessage)) {
      try {
        setLoadingCategories(true);
        await dataService.deleteCategory(categoryId);

        const updatedCategories = await dataService.getCategories();
        console.log("Updated categories after delete:", updatedCategories);

        if (updatedCategories && updatedCategories.length > 0) {
          // إزالة التكرار من الفئات
          const uniqueCategories = [
            ...new Map(
              updatedCategories.map((item) => [item.name, item])
            ).values(),
          ];
          setCategories(uniqueCategories);
        } else {
          // إعادة تحميل البيانات من جديد
          const productsData = await dataService.getProducts();
          const productCategories = [
            ...new Set(productsData.map((item) => item.category)),
          ];
          const fallbackCategories = productCategories.map((name, index) => ({
            id: index + 1,
            name,
            name_en: DataService.getCategoryEnglishName(name),
            display_order: index + 1,
            is_active: true,
            description: "",
            description_en: "",
          }));
          setCategories(fallbackCategories);
        }

        // إعادة تحميل المنتجات لتحديث العرض
        const updatedProducts = await dataService.getProducts();
        setProducts(updatedProducts);

        setLoadingCategories(false);
        alert("تم حذف الفئة بنجاح!");
      } catch (error) {
        console.error("Error deleting category:", error);
        setLoadingCategories(false);
        alert(
          error.message || "حدث خطأ أثناء حذف الفئة. يرجى المحاولة مرة أخرى."
        );
      }
    }
  };

  // دالة جديدة لحذف الفئة مع جميع منتجاتها
  const deleteCategoryWithProducts = async (categoryId, categoryName) => {
    // التحقق من وجود منتجات في هذه الفئة
    const hasProducts = products.some(
      (category) =>
        category.category === categoryName &&
        category.items &&
        category.items.length > 0
    );

    // عد المنتجات في الفئة
    const productsCount = hasProducts
      ? products.find((cat) => cat.category === categoryName)?.items?.length ||
        0
      : 0;

    let confirmMessage = `🗑️ حذف الفئة مع جميع منتجاتها\n\nهل أنت متأكد من حذف فئة "${categoryName}"`;

    if (hasProducts) {
      confirmMessage += ` مع جميع منتجاتها (${productsCount} منتج)؟\n\n⚠️ تحذير خطير:\n• سيتم حذف الفئة نهائياً\n• سيتم حذف جميع المنتجات في هذه الفئة نهائياً\n• لا يمكن التراجع عن هذا الإجراء\n\nهذا الإجراء لا يمكن التراجع عنه!`;
    } else {
      confirmMessage += `؟\n\nسيتم حذف الفئة نهائياً.\nلا يمكن التراجع عن هذا الإجراء.`;
    }

    if (window.confirm(confirmMessage)) {
      try {
        setLoadingCategories(true);

        // استخدام الدالة الجديدة لحذف الفئة مع منتجاتها
        const result = await dataService.deleteCategoryWithProducts(
          categoryId,
          categoryName
        );

        console.log("Delete result:", result);

        // إعادة تحميل البيانات
        const updatedCategories = await dataService.getCategories();
        console.log(
          "Updated categories after delete with products:",
          updatedCategories
        );

        if (updatedCategories && updatedCategories.length > 0) {
          const uniqueCategories = [
            ...new Map(
              updatedCategories.map((item) => [item.name, item])
            ).values(),
          ];
          setCategories(uniqueCategories);
        } else {
          const productsData = await dataService.getProducts();
          const productCategories = [
            ...new Set(productsData.map((item) => item.category)),
          ];
          const fallbackCategories = productCategories.map((name, index) => ({
            id: index + 1,
            name,
            name_en: DataService.getCategoryEnglishName(name),
            display_order: index + 1,
            is_active: true,
            description: "",
            description_en: "",
          }));
          setCategories(fallbackCategories);
        }

        // إعادة تحميل المنتجات لتحديث العرض
        const updatedProducts = await dataService.getProducts();
        setProducts(updatedProducts);

        setLoadingCategories(false);

        const successMessage =
          result.deletedProducts > 0
            ? `تم حذف الفئة "${categoryName}" مع ${result.deletedProducts} منتج بنجاح!`
            : `تم حذف الفئة "${categoryName}" بنجاح!`;

        alert(successMessage);
      } catch (error) {
        console.error("Error deleting category with products:", error);
        setLoadingCategories(false);
        alert(
          error.message ||
            "حدث خطأ أثناء حذف الفئة والمنتجات. يرجى المحاولة مرة أخرى."
        );
      }
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        setNewProduct({ ...newProduct, image: reader.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddProduct = async (e) => {
    e.preventDefault();

    if (
      !newProduct.name ||
      !newProduct.nameEn ||
      !newProduct.price ||
      !newProduct.image
    ) {
      alert("يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    try {
      const productData = {
        name: newProduct.name,
        nameEn: newProduct.nameEn,
        price: parseFloat(newProduct.price),
        image: newProduct.image,
        category: newProduct.category,
        desc: newProduct.desc || `${newProduct.name} طازج ولذيذ`,
        descEn: newProduct.descEn || `Fresh and delicious ${newProduct.nameEn}`,
      };

      await dataService.saveProduct(productData);
      const updatedProducts = await dataService.getProducts();
      setProducts(updatedProducts);

      setNewProduct({
        name: "",
        nameEn: "",
        price: "",
        image: "",
        category: categories.length > 0 ? categories[0].name : "",
        desc: "",
        descEn: "",
      });
      setImagePreview("");
      setShowAddForm(false);

      alert(
        "تم إضافة المنتج بنجاح! سيظهر في صفحة العملاء خلال ثوانٍ قليلة. يمكنك أيضاً الضغط على زر 'تحديث القائمة' في صفحة العملاء لرؤية المنتج فوراً."
      );
    } catch (error) {
      console.error("Error adding product:", error);
      alert("حدث خطأ أثناء إضافة المنتج. يرجى المحاولة مرة أخرى.");
    }
  };

  const startEditProduct = (product) => {
    setEditingProduct(product);
    setNewProduct({
      name: product.name,
      nameEn: product.nameEn || "",
      price: product.price.toString(),
      image: product.image,
      category: product.category,
      desc: product.desc || "",
      descEn: product.descEn || "",
    });
    setImagePreview(product.image);
    setShowEditForm(true);
  };

  const handleEditProduct = async (e) => {
    e.preventDefault();

    if (
      !newProduct.name ||
      !newProduct.nameEn ||
      !newProduct.price ||
      !newProduct.image
    ) {
      alert("يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    try {
      const productData = {
        id: editingProduct.id,
        name: newProduct.name,
        nameEn: newProduct.nameEn,
        price: parseFloat(newProduct.price),
        image: newProduct.image,
        category: newProduct.category,
        desc: newProduct.desc || `${newProduct.name} طازج ولذيذ`,
        descEn: newProduct.descEn || `Fresh and delicious ${newProduct.nameEn}`,
      };

      await dataService.updateProduct(editingProduct.id, productData);
      const updatedProducts = await dataService.getProducts();
      setProducts(updatedProducts);

      setNewProduct({
        name: "",
        nameEn: "",
        price: "",
        image: "",
        category: categories.length > 0 ? categories[0].name : "",
        desc: "",
        descEn: "",
      });
      setImagePreview("");
      setShowEditForm(false);
      setEditingProduct(null);

      alert(
        "تم تحديث المنتج بنجاح! التغييرات ستظهر في صفحة العملاء خلال ثوانٍ قليلة."
      );
    } catch (error) {
      console.error("Error updating product:", error);
      alert("حدث خطأ أثناء تحديث المنتج. يرجى المحاولة مرة أخرى.");
    }
  };

  const deleteProduct = async (productId) => {
    if (window.confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
      try {
        await dataService.deleteProduct(productId);
        const updatedProducts = await dataService.getProducts();
        setProducts(updatedProducts);
        alert("تم حذف المنتج بنجاح!");
      } catch (error) {
        console.error("Error deleting product:", error);
        alert("حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.");
      }
    }
  };

  // دالة جديدة لحذف جميع منتجات فئة معينة
  const deleteAllProductsInCategory = async (categoryName) => {
    const category = products.find((cat) => cat.category === categoryName);
    const productsCount = category?.items?.length || 0;

    if (productsCount === 0) {
      alert("لا توجد منتجات في هذه الفئة للحذف.");
      return;
    }

    const confirmMessage = `🗑️ حذف جميع منتجات الفئة\n\nهل أنت متأكد من حذف جميع المنتجات في فئة "${categoryName}"؟\n\n⚠️ تحذير:\n• سيتم حذف ${productsCount} منتج نهائياً\n• لا يمكن التراجع عن هذا الإجراء\n• ستبقى الفئة موجودة ولكن فارغة\n\nهذا الإجراء لا يمكن التراجع عنه!`;

    if (window.confirm(confirmMessage)) {
      try {
        setLoadingProducts(true);
        let deletedCount = 0;
        let failedCount = 0;

        // حذف كل منتج في الفئة
        for (const product of category.items) {
          try {
            await dataService.deleteProduct(product.id);
            deletedCount++;
            console.log(`✅ Deleted product: ${product.name}`);
          } catch (error) {
            console.error(
              `❌ Failed to delete product: ${product.name}`,
              error
            );
            failedCount++;
          }
        }

        // إعادة تحميل المنتجات
        const updatedProducts = await dataService.getProducts();
        setProducts(updatedProducts);
        setLoadingProducts(false);

        // عرض رسالة النتيجة
        if (deletedCount > 0) {
          let message = `تم حذف ${deletedCount} منتج من فئة "${categoryName}" بنجاح!`;
          if (failedCount > 0) {
            message += `\n\nتعذر حذف ${failedCount} منتج. يرجى المحاولة مرة أخرى.`;
          }
          alert(message);
        } else {
          alert("لم يتم حذف أي منتج. يرجى المحاولة مرة أخرى.");
        }
      } catch (error) {
        console.error("Error deleting products in category:", error);
        setLoadingProducts(false);
        alert("حدث خطأ أثناء حذف المنتجات. يرجى المحاولة مرة أخرى.");
      }
    }
  };

  const moveCategoryUp = (index) => {
    if (index > 0) {
      const newCategories = [...categories];
      [newCategories[index], newCategories[index - 1]] = [
        newCategories[index - 1],
        newCategories[index],
      ];
      setCategories(newCategories);
      saveCategoriesOrder(newCategories);
    }
  };

  const moveCategoryDown = (index) => {
    if (index < categories.length - 1) {
      const newCategories = [...categories];
      [newCategories[index], newCategories[index + 1]] = [
        newCategories[index + 1],
        newCategories[index],
      ];
      setCategories(newCategories);
      saveCategoriesOrder(newCategories);
    }
  };

  const saveCategoriesOrder = async (orderedCategories) => {
    try {
      // تحديث ترتيب الفئات في Supabase
      let updatedCount = 0;

      for (let i = 0; i < orderedCategories.length; i++) {
        const category = orderedCategories[i];

        // التحقق من وجود معرف صحيح للفئة
        if (
          category.id &&
          typeof category.id === "string" &&
          category.id.length > 10
        ) {
          try {
            await dataService.updateCategoryOrder(category.id, i + 1);
            updatedCount++;
          } catch (updateError) {
            console.warn(
              `Failed to update order for category: ${category.name}`,
              updateError
            );
          }
        } else {
          console.warn(
            `Category "${category.name}" does not have a valid ID, skipping order update`
          );
        }
      }

      if (updatedCount > 0) {
        console.log(`تم حفظ ترتيب ${updatedCount} فئة في Supabase`);
        showSuccessMessage(
          `تم حفظ ترتيب ${updatedCount} فئة! سيظهر الترتيب الجديد في صفحة العملاء.`
        );
      } else {
        console.log(
          "لم يتم تحديث أي فئة - تأكد من إنشاء جدول الفئات في Supabase أولاً"
        );
        showSuccessMessage(
          "تم ترتيب الفئات محلياً. لحفظ الترتيب بشكل دائم، يرجى إنشاء جدول الفئات في Supabase."
        );
      }
    } catch (error) {
      console.error("Error saving categories order:", error);
      alert("حدث خطأ أثناء حفظ ترتيب الفئات: " + error.message);
    }
  };

  // دوال ترتيب الفئات في قسم المنتجات
  const moveProductCategoryUp = async (categoryIndex) => {
    if (categoryIndex === 0) return;

    const newProducts = [...products];
    const temp = newProducts[categoryIndex];
    newProducts[categoryIndex] = newProducts[categoryIndex - 1];
    newProducts[categoryIndex - 1] = temp;

    setProducts(newProducts);

    // تحديث ترتيب الفئات في قاعدة البيانات
    await updateProductCategoriesOrder(newProducts);
  };

  const moveProductCategoryDown = async (categoryIndex) => {
    if (categoryIndex === products.length - 1) return;

    const newProducts = [...products];
    const temp = newProducts[categoryIndex];
    newProducts[categoryIndex] = newProducts[categoryIndex + 1];
    newProducts[categoryIndex + 1] = temp;

    setProducts(newProducts);

    // تحديث ترتيب الفئات في قاعدة البيانات
    await updateProductCategoriesOrder(newProducts);
  };

  const updateProductCategoriesOrder = async (orderedProducts) => {
    try {
      // البحث عن الفئات المطابقة في قائمة الفئات الأساسية
      const categoryNames = orderedProducts.map((product) => product.category);
      const orderedCategories = [];

      categoryNames.forEach((categoryName, index) => {
        const categoryData = categories.find(
          (cat) => cat.name === categoryName
        );
        if (categoryData) {
          orderedCategories.push({
            ...categoryData,
            display_order: index + 1,
          });
        }
      });

      // حفظ الترتيب الجديد
      if (orderedCategories.length > 0) {
        await saveCategoriesOrder(orderedCategories);
      }
    } catch (error) {
      console.error("Error updating product categories order:", error);
    }
  };

  const showSuccessMessage = (message) => {
    const messageDiv = document.createElement("div");
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
      z-index: 10000;
      font-weight: 600;
      max-width: 300px;
      animation: slideInRight 0.3s ease;
    `;

    if (!document.getElementById("success-message-styles")) {
      const style = document.createElement("style");
      style.id = "success-message-styles";
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(messageDiv);

    setTimeout(() => {
      messageDiv.style.animation = "slideOutRight 0.3s ease";
      setTimeout(() => {
        if (messageDiv.parentNode) {
          messageDiv.parentNode.removeChild(messageDiv);
        }
      }, 300);
    }, 4000);
  };

  // مؤشر التحميل الرئيسي
  if (loading) {
    return (
      <div className="admin-container">
        <div className="admin-header">
          <h1>لوحة تحكم الأدمن</h1>
          <p>إدارة قائمة المنتجات والأصناف في المقهى</p>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-container">
      <div className="admin-header">
        <h1>لوحة تحكم الأدمن</h1>
        <p>إدارة قائمة المنتجات والأصناف في المقهى</p>
      </div>

      <div className="admin-tabs">
        <button
          className={`admin-tab ${activeTab === "products" ? "active" : ""}`}
          onClick={() => setActiveTab("products")}
        >
          <FontAwesomeIcon icon={faEdit} />
          <span>إدارة المنتجات</span>
        </button>
        <button
          className={`admin-tab ${activeTab === "categories" ? "active" : ""}`}
          onClick={() => setActiveTab("categories")}
        >
          <FontAwesomeIcon icon={faPlus} />
          <span>إدارة الفئات</span>
        </button>
      </div>

      {activeTab === "products" && (
        <>
          <div className="admin-actions">
            <button
              className="add-product-btn"
              onClick={() => setShowAddForm(true)}
            >
              <FontAwesomeIcon icon={faPlus} />
              إضافة منتج جديد
            </button>
          </div>
        </>
      )}

      {activeTab === "categories" && (
        <>
          <div className="admin-actions">
            <button
              className="add-category-btn"
              onClick={() => setShowCategoryForm(true)}
            >
              <FontAwesomeIcon icon={faPlus} />
              إضافة فئة جديدة
            </button>
          </div>

          <div className="categories-display">
            <div className="categories-header">
              <h2>الفئات المتاحة</h2>
              <div className="categories-info">
                <FontAwesomeIcon icon={faSort} />
                <span>يمكنك ترتيب الفئات باستخدام الأسهم</span>
              </div>
            </div>

            <div className="categories-list">
              {loadingCategories ? (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>جاري تحميل الفئات...</p>
                </div>
              ) : (
                <>
                  {categories.map((category, index) => (
                    <div key={index} className="category-item">
                      <div className="category-order">
                        <span className="order-number">{index + 1}</span>
                      </div>

                      <div className="category-info">
                        <h3>{category.name}</h3>
                        {category.nameEn && (
                          <p className="category-name-en">{category.nameEn}</p>
                        )}
                        {category.description && (
                          <p className="category-desc">
                            <strong>عربي:</strong> {category.description}
                          </p>
                        )}
                        {category.description_en && (
                          <p className="category-desc-en">
                            <strong>English:</strong> {category.description_en}
                          </p>
                        )}
                      </div>

                      <div className="category-actions">
                        <button
                          className={`order-btn ${
                            index === 0 ? "disabled" : ""
                          }`}
                          onClick={() => moveCategoryUp(index)}
                          disabled={index === 0}
                          title="تحريك للأعلى"
                        >
                          <FontAwesomeIcon icon={faArrowUp} />
                        </button>
                        <button
                          className={`order-btn ${
                            index === categories.length - 1 ? "disabled" : ""
                          }`}
                          onClick={() => moveCategoryDown(index)}
                          disabled={index === categories.length - 1}
                          title="تحريك للأسفل"
                        >
                          <FontAwesomeIcon icon={faArrowDown} />
                        </button>
                        <button
                          className="edit-btn"
                          onClick={() => startEditCategory(category)}
                          title="تعديل الفئة"
                          disabled={loadingCategories}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                        <button
                          className="delete-btn"
                          onClick={() =>
                            deleteCategory(
                              category.id || category.name,
                              category.name
                            )
                          }
                          title="حذف الفئة فقط"
                          disabled={loadingCategories}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                        <button
                          className="delete-with-products-btn"
                          onClick={() =>
                            deleteCategoryWithProducts(
                              category.id || category.name,
                              category.name
                            )
                          }
                          title="حذف الفئة مع جميع منتجاتها"
                          disabled={loadingCategories}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                          <span className="delete-with-products-text">
                            +منتجات
                          </span>
                        </button>
                      </div>
                    </div>
                  ))}

                  {categories.length === 0 && !loadingCategories && (
                    <div className="empty-categories">
                      <div className="empty-icon">📂</div>
                      <h3>لا توجد فئات</h3>
                      <p>ابدأ بإضافة فئات جديدة لتنظيم المنتجات</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </>
      )}

      {showAddForm && (
        <div className="add-product-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>إضافة منتج جديد</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowAddForm(false);
                  setImagePreview("");
                  setNewProduct({
                    name: "",
                    nameEn: "",
                    price: "",
                    image: "",
                    category: categories.length > 0 ? categories[0].name : "",
                    desc: "",
                  });
                }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <form onSubmit={handleAddProduct} className="add-product-form">
              <div className="form-row">
                <div className="form-group">
                  <label>اسم المنتج (عربي) *</label>
                  <input
                    type="text"
                    value={newProduct.name}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, name: e.target.value })
                    }
                    placeholder="مثال: قهوة عربية"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>اسم المنتج (إنجليزي) *</label>
                  <input
                    type="text"
                    value={newProduct.nameEn}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, nameEn: e.target.value })
                    }
                    placeholder="Example: Arabic Coffee"
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>السعر (ر.س) *</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={newProduct.price}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, price: e.target.value })
                    }
                    placeholder="15.00"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={newProduct.category}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, category: e.target.value })
                    }
                  >
                    {categories.map((category) => (
                      <option key={category.name} value={category.name}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>الوصف (عربي - اختياري)</label>
                  <textarea
                    value={newProduct.desc}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, desc: e.target.value })
                    }
                    placeholder="وصف المنتج بالعربية..."
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف (إنجليزي - اختياري)</label>
                  <textarea
                    value={newProduct.descEn}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, descEn: e.target.value })
                    }
                    placeholder="Product description in English..."
                    rows="3"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>صورة المنتج *</label>
                <div className="image-upload">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    id="product-image"
                    required
                  />
                  <label htmlFor="product-image" className="image-upload-label">
                    <FontAwesomeIcon icon={faImage} />
                    اختر صورة المنتج
                  </label>

                  {imagePreview && (
                    <div className="image-preview">
                      <img src={imagePreview} alt="معاينة" />
                    </div>
                  )}
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="submit-btn">
                  <FontAwesomeIcon icon={faCheck} />
                  إضافة المنتج
                </button>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setShowAddForm(false);
                    setImagePreview("");
                    setNewProduct({
                      name: "",
                      nameEn: "",
                      price: "",
                      image: "",
                      category: categories.length > 0 ? categories[0].name : "",
                      desc: "",
                      descEn: "",
                    });
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showEditForm && (
        <div className="add-product-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>تعديل المنتج</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowEditForm(false);
                  setEditingProduct(null);
                  setImagePreview("");
                  setNewProduct({
                    name: "",
                    nameEn: "",
                    price: "",
                    image: "",
                    category: categories.length > 0 ? categories[0].name : "",
                    desc: "",
                    descEn: "",
                  });
                }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <form onSubmit={handleEditProduct} className="add-product-form">
              <div className="form-row">
                <div className="form-group">
                  <label>اسم المنتج (عربي) *</label>
                  <input
                    type="text"
                    value={newProduct.name}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, name: e.target.value })
                    }
                    placeholder="مثال: قهوة عربية"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>اسم المنتج (إنجليزي) *</label>
                  <input
                    type="text"
                    value={newProduct.nameEn}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, nameEn: e.target.value })
                    }
                    placeholder="Example: Arabic Coffee"
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>السعر (ر.س) *</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={newProduct.price}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, price: e.target.value })
                    }
                    placeholder="15.00"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={newProduct.category}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, category: e.target.value })
                    }
                  >
                    {categories.map((category) => (
                      <option key={category.name} value={category.name}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>الوصف (عربي - اختياري)</label>
                  <textarea
                    value={newProduct.desc}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, desc: e.target.value })
                    }
                    placeholder="وصف المنتج بالعربية..."
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>الوصف (إنجليزي - اختياري)</label>
                  <textarea
                    value={newProduct.descEn}
                    onChange={(e) =>
                      setNewProduct({ ...newProduct, descEn: e.target.value })
                    }
                    placeholder="Product description in English..."
                    rows="3"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>صورة المنتج *</label>
                <div className="image-upload">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    id="edit-product-image"
                  />
                  <label
                    htmlFor="edit-product-image"
                    className="image-upload-label"
                  >
                    <FontAwesomeIcon icon={faImage} />
                    تغيير صورة المنتج
                  </label>

                  {imagePreview && (
                    <div className="image-preview">
                      <img src={imagePreview} alt="معاينة" />
                    </div>
                  )}
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="submit-btn">
                  <FontAwesomeIcon icon={faCheck} />
                  حفظ التغييرات
                </button>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setShowEditForm(false);
                    setEditingProduct(null);
                    setImagePreview("");
                    setNewProduct({
                      name: "",
                      nameEn: "",
                      price: "",
                      image: "",
                      category: categories.length > 0 ? categories[0].name : "",
                      desc: "",
                      descEn: "",
                    });
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showCategoryForm && (
        <div className="add-product-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>إضافة فئة جديدة</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowCategoryForm(false);
                  setNewCategory({
                    name: "",
                    nameEn: "",
                    description: "",
                    descriptionEn: "",
                  });
                }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <form onSubmit={handleAddCategory} className="add-product-form">
              <div className="form-row">
                <div className="form-group">
                  <label>اسم الفئة (عربي) *</label>
                  <input
                    type="text"
                    value={newCategory.name}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, name: e.target.value })
                    }
                    placeholder="مثال: مشروبات ساخنة"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>اسم الفئة (إنجليزي)</label>
                  <input
                    type="text"
                    value={newCategory.nameEn}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, nameEn: e.target.value })
                    }
                    placeholder="Example: Hot Drinks"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>وصف الفئة (عربي - اختياري)</label>
                  <textarea
                    value={newCategory.description}
                    onChange={(e) =>
                      setNewCategory({
                        ...newCategory,
                        description: e.target.value,
                      })
                    }
                    placeholder="وصف الفئة بالعربية..."
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>وصف الفئة (إنجليزي - اختياري)</label>
                  <textarea
                    value={newCategory.descriptionEn}
                    onChange={(e) =>
                      setNewCategory({
                        ...newCategory,
                        descriptionEn: e.target.value,
                      })
                    }
                    placeholder="Category description in English..."
                    rows="3"
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="submit-btn">
                  <FontAwesomeIcon icon={faCheck} />
                  إضافة الفئة
                </button>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setShowCategoryForm(false);
                    setNewCategory({
                      name: "",
                      nameEn: "",
                      description: "",
                      descriptionEn: "",
                    });
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showEditCategoryForm && (
        <div className="add-product-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>تعديل الفئة</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowEditCategoryForm(false);
                  setEditingCategory(null);
                  setNewCategory({
                    name: "",
                    nameEn: "",
                    description: "",
                    descriptionEn: "",
                  });
                }}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <form onSubmit={handleEditCategory} className="add-product-form">
              <div className="form-row">
                <div className="form-group">
                  <label>اسم الفئة (عربي) *</label>
                  <input
                    type="text"
                    value={newCategory.name}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, name: e.target.value })
                    }
                    placeholder="مثال: مشروبات ساخنة"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>اسم الفئة (إنجليزي)</label>
                  <input
                    type="text"
                    value={newCategory.nameEn}
                    onChange={(e) =>
                      setNewCategory({ ...newCategory, nameEn: e.target.value })
                    }
                    placeholder="Example: Hot Drinks"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>وصف الفئة (عربي - اختياري)</label>
                  <textarea
                    value={newCategory.description}
                    onChange={(e) =>
                      setNewCategory({
                        ...newCategory,
                        description: e.target.value,
                      })
                    }
                    placeholder="وصف الفئة بالعربية..."
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>وصف الفئة (إنجليزي - اختياري)</label>
                  <textarea
                    value={newCategory.descriptionEn}
                    onChange={(e) =>
                      setNewCategory({
                        ...newCategory,
                        descriptionEn: e.target.value,
                      })
                    }
                    placeholder="Category description in English..."
                    rows="3"
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="submit-btn">
                  <FontAwesomeIcon icon={faCheck} />
                  حفظ التغييرات
                </button>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setShowEditCategoryForm(false);
                    setEditingCategory(null);
                    setNewCategory({
                      name: "",
                      nameEn: "",
                      description: "",
                      descriptionEn: "",
                    });
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {activeTab === "products" && (
        <div className="products-display">
          {loadingProducts ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>جاري تحميل المنتجات...</p>
            </div>
          ) : (
            <>
              {products.map((category, categoryIndex) => (
                <div key={category.category} className="product-category">
                  <h2 className="category-title">
                    <div className="category-title-content">
                      <span className="category-name">
                        {category.category}
                        <span className="items-count">
                          ({category.items.length} منتج)
                        </span>
                      </span>
                      <div className="category-order-controls">
                        <button
                          className="order-btn"
                          onClick={() => moveProductCategoryUp(categoryIndex)}
                          disabled={categoryIndex === 0}
                          title="نقل الفئة للأعلى"
                        >
                          <FontAwesomeIcon icon={faArrowUp} />
                        </button>
                        <button
                          className="order-btn"
                          onClick={() => moveProductCategoryDown(categoryIndex)}
                          disabled={categoryIndex === products.length - 1}
                          title="نقل الفئة للأسفل"
                        >
                          <FontAwesomeIcon icon={faArrowDown} />
                        </button>
                        <button
                          className="delete-category-products-btn"
                          onClick={() =>
                            deleteAllProductsInCategory(category.category)
                          }
                          title="حذف جميع منتجات هذه الفئة"
                          disabled={
                            !category.items || category.items.length === 0
                          }
                        >
                          <FontAwesomeIcon icon={faTrash} />
                          <span className="delete-text">حذف الكل</span>
                        </button>
                      </div>
                    </div>
                  </h2>

                  <div className="products-grid">
                    {category.items.map((product) => (
                      <div key={product.id} className="product-card">
                        <div className="product-image">
                          <img src={product.image} alt={product.name} />
                        </div>

                        <div className="product-details">
                          <h3 className="product-name">{product.name}</h3>
                          {product.nameEn && (
                            <p className="product-name-en">{product.nameEn}</p>
                          )}
                          {product.desc && (
                            <p className="product-desc">
                              <strong>عربي:</strong> {product.desc}
                            </p>
                          )}
                          {product.descEn && (
                            <p className="product-desc-en">
                              <strong>English:</strong> {product.descEn}
                            </p>
                          )}
                          <div className="product-price">
                            {product.price} ر.س
                          </div>
                        </div>

                        <div className="product-actions">
                          <button
                            className="edit-btn"
                            onClick={() => startEditProduct(product)}
                            title="تعديل المنتج"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </button>
                          <button
                            className="delete-btn"
                            onClick={() => deleteProduct(product.id)}
                            title="حذف المنتج"
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {products.length === 0 && !loadingProducts && (
                <div className="empty-state">
                  <div className="empty-icon">📦</div>
                  <h3>لا توجد منتجات</h3>
                  <p>ابدأ بإضافة منتجات جديدة لقائمة المقهى</p>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Admin;
