-- إصلاح البيانات الموجودة في جدول orders
-- هذا الملف يصحح أنواع البيانات للسجلات الموجودة

-- أولاً: التحقق من البيانات الحالية
SELECT 
    id,
    table_number,
    customer_name,
    subtotal,
    tax,
    total,
    status,
    created_at
FROM "public"."orders" 
ORDER BY created_at DESC;

-- إذا كانت الأعمدة من نوع VARCHAR وتحتاج لتحويل:

-- الطريقة 1: تحديث أنواع الأعمدة (إذا كانت VARCHAR)
-- تحذير: هذا قد يفشل إذا كانت هناك بيانات غير صالحة

-- تحويل table_number إلى INTEGER
ALTER TABLE "public"."orders" 
ALTER COLUMN "table_number" TYPE INTEGER USING "table_number"::INTEGER;

-- تحويل subtotal إلى DECIMAL
ALTER TABLE "public"."orders" 
ALTER COLUMN "subtotal" TYPE DECIMAL(10,2) USING "subtotal"::DECIMAL(10,2);

-- تحويل tax إلى DECIMAL
ALTER TABLE "public"."orders" 
ALTER COLUMN "tax" TYPE DECIMAL(10,2) USING "tax"::DECIMAL(10,2);

-- تحويل total إلى DECIMAL
ALTER TABLE "public"."orders" 
ALTER COLUMN "total" TYPE DECIMAL(10,2) USING "total"::DECIMAL(10,2);

-- الطريقة 2: إذا فشلت الطريقة الأولى، استخدم هذه الطريقة
-- (إنشاء جدول جديد ونقل البيانات)

/*
-- إنشاء جدول مؤقت بالبنية الصحيحة
CREATE TABLE "public"."orders_new" (
    "id" UUID PRIMARY KEY,
    "table_number" INTEGER NOT NULL,
    "customer_name" VARCHAR(255) DEFAULT '',
    "notes" TEXT DEFAULT '',
    "twitter_handle" VARCHAR(100),
    "subtotal" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "tax" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "total" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "assigned_waiter" VARCHAR(255),
    "waiter_id" UUID,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

-- نقل البيانات مع التحويل
INSERT INTO "public"."orders_new" (
    "id", 
    "table_number", 
    "customer_name", 
    "notes", 
    "twitter_handle", 
    "subtotal", 
    "tax", 
    "total", 
    "status", 
    "assigned_waiter", 
    "waiter_id", 
    "created_at", 
    "updated_at"
)
SELECT 
    "id"::UUID,
    "table_number"::INTEGER,
    "customer_name",
    "notes",
    "twitter_handle",
    "subtotal"::DECIMAL(10,2),
    "tax"::DECIMAL(10,2),
    "total"::DECIMAL(10,2),
    "status",
    "assigned_waiter",
    "waiter_id"::UUID,
    "created_at"::TIMESTAMPTZ,
    "updated_at"::TIMESTAMPTZ
FROM "public"."orders";

-- حذف الجدول القديم
DROP TABLE "public"."orders";

-- إعادة تسمية الجدول الجديد
ALTER TABLE "public"."orders_new" RENAME TO "orders";
*/

-- التحقق من النتيجة النهائية
SELECT 
    id,
    table_number,
    customer_name,
    subtotal,
    tax,
    total,
    status,
    created_at,
    pg_typeof(table_number) as table_number_type,
    pg_typeof(subtotal) as subtotal_type,
    pg_typeof(tax) as tax_type,
    pg_typeof(total) as total_type
FROM "public"."orders" 
ORDER BY created_at DESC;

-- إضافة القيود بعد التحويل
ALTER TABLE "public"."orders" 
ADD CONSTRAINT IF NOT EXISTS "check_table_number_positive" 
CHECK ("table_number" > 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT IF NOT EXISTS "check_subtotal_non_negative" 
CHECK ("subtotal" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT IF NOT EXISTS "check_tax_non_negative" 
CHECK ("tax" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT IF NOT EXISTS "check_total_non_negative" 
CHECK ("total" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT IF NOT EXISTS "check_status_valid" 
CHECK ("status" IN ('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'));

-- رسالة نجاح
SELECT 'تم إصلاح جدول orders بنجاح!' as message;
