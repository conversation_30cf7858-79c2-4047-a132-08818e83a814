-- إنشاء جدول الطلبات بالبنية الصحيحة
-- هذا الملف يحتوي على البنية الصحيحة لجدول orders

-- حذف الجدول إذا كان موجوداً (احذر: سيمحو جميع البيانات!)
-- DROP TABLE IF EXISTS "public"."orders";

-- إ<PERSON><PERSON><PERSON><PERSON> جدول الطلبات بالبنية الصحيحة
CREATE TABLE IF NOT EXISTS "public"."orders" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "table_number" INTEGER NOT NULL,                    -- ✅ رقم صحيح وليس نص
    "customer_name" VARCHAR(255) DEFAULT '',
    "notes" TEXT DEFAULT '',
    "twitter_handle" VARCHAR(100),
    "subtotal" DECIMAL(10,2) NOT NULL DEFAULT 0.00,     -- ✅ رقم عشري وليس نص
    "tax" DECIMAL(10,2) NOT NULL DEFAULT 0.00,          -- ✅ رقم عشري وليس نص
    "total" DECIMAL(10,2) NOT NULL DEFAULT 0.00,        -- ✅ رقم عشري وليس نص
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "assigned_waiter" VARCHAR(255),
    "waiter_id" UUID,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS "idx_orders_table_number" ON "public"."orders" ("table_number");
CREATE INDEX IF NOT EXISTS "idx_orders_status" ON "public"."orders" ("status");
CREATE INDEX IF NOT EXISTS "idx_orders_created_at" ON "public"."orders" ("created_at");

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON "public"."orders" 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- إضافة قيود للتحقق من صحة البيانات
ALTER TABLE "public"."orders" 
ADD CONSTRAINT "check_table_number_positive" 
CHECK ("table_number" > 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT "check_subtotal_non_negative" 
CHECK ("subtotal" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT "check_tax_non_negative" 
CHECK ("tax" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT "check_total_non_negative" 
CHECK ("total" >= 0);

ALTER TABLE "public"."orders" 
ADD CONSTRAINT "check_status_valid" 
CHECK ("status" IN ('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'));

-- تعليق على الجدول والأعمدة
COMMENT ON TABLE "public"."orders" IS 'جدول الطلبات - يحتوي على معلومات الطلبات الأساسية';
COMMENT ON COLUMN "public"."orders"."id" IS 'معرف فريد للطلب';
COMMENT ON COLUMN "public"."orders"."table_number" IS 'رقم الطاولة (رقم صحيح)';
COMMENT ON COLUMN "public"."orders"."customer_name" IS 'اسم العميل';
COMMENT ON COLUMN "public"."orders"."notes" IS 'ملاحظات خاصة بالطلب';
COMMENT ON COLUMN "public"."orders"."twitter_handle" IS 'حساب تويتر للعميل';
COMMENT ON COLUMN "public"."orders"."subtotal" IS 'المجموع الفرعي (قبل الضريبة)';
COMMENT ON COLUMN "public"."orders"."tax" IS 'قيمة الضريبة';
COMMENT ON COLUMN "public"."orders"."total" IS 'المجموع الإجمالي (بعد الضريبة)';
COMMENT ON COLUMN "public"."orders"."status" IS 'حالة الطلب';
COMMENT ON COLUMN "public"."orders"."assigned_waiter" IS 'النادل المسؤول عن الطلب';
COMMENT ON COLUMN "public"."orders"."waiter_id" IS 'معرف النادل';
COMMENT ON COLUMN "public"."orders"."created_at" IS 'تاريخ ووقت إنشاء الطلب';
COMMENT ON COLUMN "public"."orders"."updated_at" IS 'تاريخ ووقت آخر تحديث للطلب';

-- عرض بنية الجدول للتحقق
-- \d "public"."orders"
