-- بيانات تجريبية شاملة للجدول الجديد
-- تحتوي على: اسم المنتج + سعره + الضريبة + اسم العميل + الملاحظات + رقم الطاولة

-- مسح البيانات الموجودة (اختياري)
-- DELETE FROM "public"."complete_orders";

-- إدراج بيانات تجريبية شاملة
INSERT INTO "public"."complete_orders" (
    "order_id",
    "table_number",
    "customer_name",
    "notes",
    "product_name",
    "product_description",
    "unit_price",
    "quantity",
    "tax_rate",
    "status",
    "assigned_waiter",
    "twitter_handle"
) VALUES 

-- 🍽️ طلب رقم 1 - طاولة 5 - أحمد محمد
('11111111-1111-1111-1111-111111111111', 5, 'أحمد محمد', 'بدون سكر، قهوة قوية', 'قهوة تركية', 'قهوة بن عربي أصيلة مع الهيل', 15.00, 2, 15.00, 'pending', 'سالم أحمد', '@ahmed_coffee'),
('11111111-1111-1111-1111-111111111111', 5, 'أحمد محمد', 'بدون سكر، قهوة قوية', 'كابتشينو', 'إسبريسو مع حليب مبخر ورغوة', 18.00, 1, 15.00, 'pending', 'سالم أحمد', '@ahmed_coffee'),
('11111111-1111-1111-1111-111111111111', 5, 'أحمد محمد', 'بدون سكر، قهوة قوية', 'كرواسون', 'كرواسون فرنسي طازج مع الزبدة', 12.00, 2, 15.00, 'pending', 'سالم أحمد', '@ahmed_coffee'),

-- 🍽️ طلب رقم 2 - طاولة 3 - فاطمة علي
('22222222-2222-2222-2222-222222222222', 3, 'فاطمة علي', 'سكر خفيف، حليب إضافي', 'لاتيه', 'إسبريسو مع حليب ساخن وطبقة رغوة', 20.00, 1, 15.00, 'confirmed', 'محمد سعد', '@fatima_latte'),
('22222222-2222-2222-2222-222222222222', 3, 'فاطمة علي', 'سكر خفيف، حليب إضافي', 'تشيز كيك', 'تشيز كيك نيويورك بالفراولة', 25.00, 1, 15.00, 'confirmed', 'محمد سعد', '@fatima_latte'),
('22222222-2222-2222-2222-222222222222', 3, 'فاطمة علي', 'سكر خفيف، حليب إضافي', 'عصير برتقال', 'عصير برتقال طازج 100%', 15.00, 2, 15.00, 'confirmed', 'محمد سعد', '@fatima_latte'),

-- 🍽️ طلب رقم 3 - طاولة 7 - محمد سالم
('33333333-3333-3333-3333-333333333333', 7, 'محمد سالم', 'بدون حليب، قهوة سادة', 'إسبريسو', 'قهوة إسبريسو قوية ومركزة', 10.00, 3, 15.00, 'preparing', 'أحمد علي', null),
('33333333-3333-3333-3333-333333333333', 7, 'محمد سالم', 'بدون حليب، قهوة سادة', 'مافين شوكولاتة', 'مافين بالشوكولاتة والجوز', 14.00, 2, 15.00, 'preparing', 'أحمد علي', null),

-- 🍽️ طلب رقم 4 - طاولة 2 - سارة أحمد
('44444444-4444-4444-4444-444444444444', 2, 'سارة أحمد', 'مشروب بارد، بدون كافيين', 'فرابتشينو كراميل', 'مشروب بارد بالكراميل والكريمة', 22.00, 1, 15.00, 'ready', 'سالم أحمد', '@sara_cold'),
('44444444-4444-4444-4444-444444444444', 2, 'سارة أحمد', 'مشروب بارد، بدون كافيين', 'سلطة فواكه', 'سلطة فواكه طازجة متنوعة', 18.00, 1, 15.00, 'ready', 'سالم أحمد', '@sara_cold'),
('44444444-4444-4444-4444-444444444444', 2, 'سارة أحمد', 'مشروب بارد، بدون كافيين', 'ماء معدني', 'ماء معدني فوار بالليمون', 8.00, 2, 15.00, 'ready', 'سالم أحمد', '@sara_cold'),

-- 🍽️ طلب رقم 5 - طاولة 10 - خالد محمد
('55555555-5555-5555-5555-555555555555', 10, 'خالد محمد', 'طلب سريع، للتيك أواي', 'أمريكانو', 'قهوة أمريكانو كلاسيكية', 16.00, 2, 15.00, 'delivered', 'محمد سعد', '@khalid_takeaway'),
('55555555-5555-5555-5555-555555555555', 10, 'خالد محمد', 'طلب سريع، للتيك أواي', 'ساندويش تونة', 'ساندويش تونة مع الخضار', 20.00, 1, 15.00, 'delivered', 'محمد سعد', '@khalid_takeaway'),

-- 🍽️ طلب رقم 6 - طاولة 1 - نورا سعد
('66666666-6666-6666-6666-666666666666', 1, 'نورا سعد', 'مناسبة خاصة، تقديم جميل', 'موكا', 'موكا بالشوكولاتة والكريمة', 24.00, 1, 15.00, 'cancelled', 'أحمد علي', '@nora_special'),
('66666666-6666-6666-6666-666666666666', 1, 'نورا سعد', 'مناسبة خاصة، تقديم جميل', 'تارت الفراولة', 'تارت الفراولة الطازجة', 28.00, 1, 15.00, 'cancelled', 'أحمد علي', '@nora_special');

-- عرض البيانات بالتنسيق المطلوب
SELECT 
    '📋 تقرير الطلبات الشامل' as report_title;

SELECT 
    order_id as "معرف الطلب",
    table_number as "رقم الطاولة",
    customer_name as "اسم العميل",
    product_name as "اسم المنتج",
    unit_price as "سعر الوحدة",
    quantity as "الكمية",
    item_total as "إجمالي المنتج",
    tax_rate as "معدل الضريبة (%)",
    tax_amount as "قيمة الضريبة",
    total_with_tax as "المجموع مع الضريبة",
    notes as "الملاحظات",
    status as "الحالة",
    assigned_waiter as "النادل المسؤول",
    created_at as "تاريخ الطلب"
FROM "public"."complete_orders" 
ORDER BY created_at DESC, order_id, product_name;

-- ملخص الطلبات حسب الطاولة
SELECT 
    '📊 ملخص الطلبات حسب الطاولة' as summary_title;

SELECT 
    table_number as "رقم الطاولة",
    customer_name as "اسم العميل",
    COUNT(*) as "عدد المنتجات",
    SUM(quantity) as "إجمالي الكمية",
    ROUND(SUM(item_total), 2) as "المجموع الفرعي",
    ROUND(SUM(tax_amount), 2) as "إجمالي الضريبة",
    ROUND(SUM(total_with_tax), 2) as "المجموع النهائي",
    notes as "الملاحظات",
    status as "الحالة"
FROM "public"."complete_orders" 
GROUP BY order_id, table_number, customer_name, notes, status
ORDER BY table_number;

-- إحصائيات عامة
SELECT 
    '📈 إحصائيات عامة' as stats_title;

SELECT 
    'إجمالي الطلبات' as "المؤشر",
    COUNT(DISTINCT order_id) as "القيمة"
FROM "public"."complete_orders"

UNION ALL

SELECT 
    'إجمالي المنتجات',
    COUNT(*) as value
FROM "public"."complete_orders"

UNION ALL

SELECT 
    'إجمالي العملاء',
    COUNT(DISTINCT customer_name) as value
FROM "public"."complete_orders"
WHERE customer_name != ''

UNION ALL

SELECT 
    'إجمالي المبيعات (ر.س)',
    ROUND(SUM(total_with_tax), 2) as value
FROM "public"."complete_orders"

UNION ALL

SELECT 
    'إجمالي الضرائب (ر.س)',
    ROUND(SUM(tax_amount), 2) as value
FROM "public"."complete_orders";

-- أكثر المنتجات طلباً
SELECT 
    '🏆 أكثر المنتجات طلباً' as top_products_title;

SELECT 
    product_name as "اسم المنتج",
    COUNT(*) as "عدد مرات الطلب",
    SUM(quantity) as "إجمالي الكمية",
    ROUND(AVG(unit_price), 2) as "متوسط السعر",
    ROUND(SUM(total_with_tax), 2) as "إجمالي المبيعات"
FROM "public"."complete_orders" 
GROUP BY product_name
ORDER BY SUM(total_with_tax) DESC
LIMIT 5;

SELECT 'تم إدراج البيانات التجريبية بنجاح! 🎉' as success_message;
