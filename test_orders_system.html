<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الطلبات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        .button.info {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        #output {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .order-card {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .order-items {
            margin-top: 10px;
        }
        .order-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الطلبات</h1>
        
        <div class="test-section">
            <h3>📋 اختبار جلب الطلبات</h3>
            <p>اختبار جلب الطلبات من قاعدة البيانات وعرضها</p>
            <button class="button" onclick="testGetOrders()">🔍 جلب الطلبات</button>
            <button class="button info" onclick="testGetOrdersFromBothTables()">📊 جلب من كلا الجدولين</button>
        </div>

        <div class="test-section">
            <h3>💾 اختبار حفظ طلب جديد</h3>
            <p>إنشاء طلب تجريبي وحفظه في قاعدة البيانات</p>
            <button class="button" onclick="testSaveOrder()">📦 إنشاء طلب تجريبي</button>
            <button class="button info" onclick="testSaveOrderToCompleteTable()">🗃️ حفظ في الجدول الشامل</button>
        </div>

        <div class="test-section">
            <h3>🔄 اختبار تحديث حالة الطلب</h3>
            <p>تحديث حالة طلب موجود</p>
            <button class="button" onclick="testUpdateOrderStatus()">✅ تحديث حالة الطلب</button>
        </div>

        <div class="test-section">
            <h3>🧹 تنظيف البيانات</h3>
            <p>حذف الطلبات التجريبية</p>
            <button class="button danger" onclick="clearTestOrders()">🗑️ حذف الطلبات التجريبية</button>
        </div>

        <div id="output"></div>
        <div id="orders-display"></div>
    </div>

    <script>
        let output = document.getElementById('output');
        let ordersDisplay = document.getElementById('orders-display');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00bcd4',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            
            output.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">
                [${timestamp}] ${message}
            </div>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        async function testGetOrders() {
            log('🔍 اختبار جلب الطلبات...', 'info');
            
            try {
                // جلب من الجدول القديم
                const oldOrdersResponse = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/orders?select=*,order_items(*,products(*))', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                    }
                });

                if (oldOrdersResponse.ok) {
                    const oldOrders = await oldOrdersResponse.json();
                    log(`✅ تم جلب ${oldOrders.length} طلب من الجدول القديم`, 'success');
                    
                    displayOrders(oldOrders, 'الطلبات من الجدول القديم');
                } else {
                    log('❌ فشل جلب الطلبات من الجدول القديم: ' + oldOrdersResponse.status, 'error');
                }

                // جلب من الجدول الجديد
                const completeOrdersResponse = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/complete_orders?select=*', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                    }
                });

                if (completeOrdersResponse.ok) {
                    const completeOrders = await completeOrdersResponse.json();
                    log(`✅ تم جلب ${completeOrders.length} عنصر من الجدول الشامل`, 'success');
                    
                    displayCompleteOrders(completeOrders, 'العناصر من الجدول الشامل');
                } else {
                    log('❌ فشل جلب البيانات من الجدول الشامل: ' + completeOrdersResponse.status, 'error');
                }

            } catch (error) {
                log('❌ خطأ في اختبار جلب الطلبات: ' + error.message, 'error');
            }
        }

        async function testSaveOrder() {
            log('📦 اختبار حفظ طلب جديد...', 'info');
            
            const testOrder = {
                id: 'test-order-' + Date.now(),
                tableNumber: 7,
                customerName: 'عميل تجريبي - ' + new Date().toLocaleTimeString('ar-SA'),
                notes: 'طلب تجريبي من صفحة الاختبار',
                subtotal: 25.00,
                tax: 3.75,
                total: 28.75,
                status: 'pending',
                items: [
                    {
                        id: 1,
                        name: 'قهوة تركية',
                        desc: 'قهوة بن عربي أصيلة',
                        price: 15.00,
                        qty: 1
                    },
                    {
                        id: 2,
                        name: 'كرواسون',
                        desc: 'كرواسون فرنسي طازج',
                        price: 10.00,
                        qty: 1
                    }
                ]
            };

            try {
                // محاولة حفظ الطلب
                const orderResponse = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/orders', {
                    method: 'POST',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify({
                        table_number: testOrder.tableNumber,
                        customer_name: testOrder.customerName,
                        notes: testOrder.notes,
                        subtotal: testOrder.subtotal,
                        tax: testOrder.tax,
                        total: testOrder.total,
                        status: testOrder.status
                    })
                });

                if (orderResponse.ok) {
                    const savedOrder = await orderResponse.json();
                    log(`✅ تم حفظ الطلب بنجاح! ID: ${savedOrder[0].id}`, 'success');
                    
                    // حفظ عناصر الطلب
                    const orderItems = testOrder.items.map(item => ({
                        order_id: savedOrder[0].id,
                        product_id: item.id,
                        quantity: item.qty,
                        unit_price: item.price,
                        total_price: item.price * item.qty
                    }));

                    const itemsResponse = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/order_items', {
                        method: 'POST',
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                            'Content-Type': 'application/json',
                            'Prefer': 'return=representation'
                        },
                        body: JSON.stringify(orderItems)
                    });

                    if (itemsResponse.ok) {
                        const savedItems = await itemsResponse.json();
                        log(`✅ تم حفظ ${savedItems.length} عنصر للطلب`, 'success');
                    } else {
                        log('❌ فشل حفظ عناصر الطلب: ' + itemsResponse.status, 'error');
                    }

                } else {
                    log('❌ فشل حفظ الطلب: ' + orderResponse.status, 'error');
                }

            } catch (error) {
                log('❌ خطأ في حفظ الطلب: ' + error.message, 'error');
            }
        }

        function displayOrders(orders, title) {
            let html = `<h3>${title}</h3>`;
            
            orders.forEach(order => {
                html += `
                    <div class="order-card">
                        <div class="order-header">
                            <strong>طاولة ${order.table_number} - ${order.customer_name || 'بدون اسم'}</strong>
                            <span style="background: #4CAF50; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                ${order.status}
                            </span>
                        </div>
                        <div>المجموع: ${order.total} ر.س</div>
                        <div>الملاحظات: ${order.notes || 'لا توجد'}</div>
                        <div class="order-items">
                            <strong>العناصر:</strong>
                            ${order.order_items ? order.order_items.map(item => `
                                <div class="order-item">
                                    <span>${item.products?.name || 'منتج غير محدد'} x${item.quantity}</span>
                                    <span>${item.total_price} ر.س</span>
                                </div>
                            `).join('') : '<div>لا توجد عناصر</div>'}
                        </div>
                    </div>
                `;
            });
            
            ordersDisplay.innerHTML = html;
        }

        function displayCompleteOrders(items, title) {
            let html = `<h3>${title}</h3>`;
            
            // تجميع العناصر حسب order_id
            const ordersMap = {};
            items.forEach(item => {
                if (!ordersMap[item.order_id]) {
                    ordersMap[item.order_id] = {
                        order_id: item.order_id,
                        table_number: item.table_number,
                        customer_name: item.customer_name,
                        status: item.status,
                        notes: item.notes,
                        items: []
                    };
                }
                ordersMap[item.order_id].items.push(item);
            });

            Object.values(ordersMap).forEach(order => {
                const total = order.items.reduce((sum, item) => sum + (item.total_with_tax || 0), 0);
                
                html += `
                    <div class="order-card">
                        <div class="order-header">
                            <strong>طاولة ${order.table_number} - ${order.customer_name || 'بدون اسم'}</strong>
                            <span style="background: #4CAF50; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                ${order.status}
                            </span>
                        </div>
                        <div>المجموع: ${total.toFixed(2)} ر.س</div>
                        <div>الملاحظات: ${order.notes || 'لا توجد'}</div>
                        <div class="order-items">
                            <strong>العناصر:</strong>
                            ${order.items.map(item => `
                                <div class="order-item">
                                    <span>${item.product_name} x${item.quantity}</span>
                                    <span>${(item.total_with_tax || 0).toFixed(2)} ر.س</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            ordersDisplay.innerHTML = html;
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 مرحباً بك في صفحة اختبار نظام الطلبات', 'success');
            log('💡 استخدم الأزرار أعلاه لاختبار وظائف النظام', 'info');
        };
    </script>
</body>
</html>
