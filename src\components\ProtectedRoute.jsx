import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "./AuthProvider";

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { currentUser, isAuthenticated, hasRole } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  // إذا لم يكن المستخدم مسجل دخول
  if (!isAuthenticated()) {
    return <div>جاري التحويل...</div>;
  }

  // إذا كان هناك أدوار محددة مطلوبة
  if (allowedRoles.length > 0) {
    const userHasPermission = allowedRoles.some((role) => hasRole(role));

    if (!userHasPermission) {
      return (
        <div className="access-denied">
          <div className="access-denied-card">
            <h2>🚫 غير مصرح لك بالوصول</h2>
            <p>ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
            <p>
              نوع حسابك: <strong>{currentUser.type}</strong>
            </p>
            <p>
              الأدوار المطلوبة: <strong>{allowedRoles.join(", ")}</strong>
            </p>
          </div>
        </div>
      );
    }
  }

  return children;
};

export default ProtectedRoute;
