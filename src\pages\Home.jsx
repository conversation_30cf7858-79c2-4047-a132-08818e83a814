import React, { useState, useEffect } from "react";
import MenuItem from "../components/MenuItem";
import CategoryTabs from "../components/CategoryTabs";
import Cart from "../components/Cart";
import LanguageSwitcher from "../components/LanguageSwitcher";
import { menuData } from "../data/menuData";
import { useOrder } from "../context/OrderContext";
import { useLanguage } from "../context/LanguageContext";
import { getTableNumberFromUrl } from "../utils/qrGenerator";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTable, faQrcode, faSync } from "@fortawesome/free-solid-svg-icons";
import dataService from "../services/dataService";

const Home = () => {
  const { t, currentLanguage } = useLanguage();
  const [activeCategory, setActiveCategory] = useState(t.allCategories);
  const [customerName, setCustomerName] = useState("");
  const [orderNotes, setOrderNotes] = useState("");
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [currentMenuData, setCurrentMenuData] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { state, actions } = useOrder();

  // تحميل البيانات من dataService
  useEffect(() => {
    const loadData = async () => {
      try {
        // تحميل الفئات أولاً
        const categoriesData = await dataService.getCategories();
        console.log("Loaded categories:", categoriesData);

        if (categoriesData && categoriesData.length > 0) {
          setCategories(categoriesData);
        }

        // تحميل المنتجات
        const products = await dataService.getProducts();
        console.log("Loaded products:", products);

        if (products && products.length > 0) {
          setCurrentMenuData(products);

          // إذا لم نحصل على فئات من قاعدة البيانات، استخرجها من المنتجات
          if (!categoriesData || categoriesData.length === 0) {
            const productCategories = [
              ...new Set(products.map((item) => item.category)),
            ];
            setCategories(
              productCategories.map((name, index) => ({
                name,
                display_order: index + 1,
              }))
            );
          }
        } else {
          // استخدام البيانات الافتراضية إذا لم توجد منتجات
          setCurrentMenuData(menuData);

          if (!categoriesData || categoriesData.length === 0) {
            const productCategories = [
              ...new Set(menuData.map((item) => item.category)),
            ];
            setCategories(
              productCategories.map((name, index) => ({
                name,
                display_order: index + 1,
              }))
            );
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        // استخدام البيانات الافتراضية في حالة الخطأ
        setCurrentMenuData(menuData);
        const productCategories = [
          ...new Set(menuData.map((item) => item.category)),
        ];
        setCategories(
          productCategories.map((name, index) => ({
            name,
            display_order: index + 1,
          }))
        );
      } finally {
        setLoadingCategories(false);
      }
    };

    loadData();

    // إعادة تحميل البيانات كل 30 ثانية للحصول على أحدث ترتيب
    const interval = setInterval(loadData, 30000);

    return () => clearInterval(interval);
  }, []);

  // Check for table number from QR code on component mount
  useEffect(() => {
    const tableNumber = getTableNumberFromUrl();
    if (tableNumber) {
      actions.setTableNumber(parseInt(tableNumber));
    }
  }, [actions]);

  // Filter menu items based on active category
  const getFilteredMenuData = () => {
    if (activeCategory === "جميع الأصناف") {
      return currentMenuData;
    }
    return currentMenuData.filter(
      (section) => section.category === activeCategory
    );
  };

  // Handle adding item to cart
  const handleAddToCart = (item, qty) => {
    actions.addToCart(item, qty);
  };

  // Handle cart quantity update
  const handleUpdateCartQty = (itemId, qty) => {
    actions.updateCartQty(itemId, qty);
  };

  // Handle removing item from cart
  const handleRemoveFromCart = (itemId) => {
    actions.removeFromCart(itemId);
  };

  // Handle manual refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      console.log("🔄 Refreshing data...");

      // تحميل الفئات مرتبة أولاً
      const categoriesData = await dataService.getCategories();
      console.log("Refreshed categories:", categoriesData);

      if (categoriesData && categoriesData.length > 0) {
        setCategories(categoriesData);
      }

      // تحميل المنتجات (ستكون مرتبة حسب ترتيب الفئات)
      const products = await dataService.getProducts();
      console.log("Refreshed products:", products);

      if (products && products.length > 0) {
        setCurrentMenuData(products);

        // إذا لم نحصل على فئات، استخرجها من المنتجات
        if (!categoriesData || categoriesData.length === 0) {
          const productCategories = [
            ...new Set(products.map((item) => item.category)),
          ];
          setCategories(
            productCategories.map((name, index) => ({
              name,
              display_order: index + 1,
            }))
          );
        }
      }

      console.log("✅ Data refreshed successfully");
    } catch (error) {
      console.error("❌ Error refreshing data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle checkout
  const handleCheckout = () => {
    if (state.cart.length === 0) return;

    if (!state.tableNumber) {
      alert("يرجى تحديد رقم الطاولة أولاً");
      return;
    }

    setShowCheckoutModal(true);
  };

  // Confirm order
  const confirmOrder = () => {
    const subtotal = state.cart.reduce(
      (sum, item) => sum + item.qty * item.price,
      0
    );
    const tax = subtotal * 0.15;
    const total = subtotal + tax;

    const orderData = {
      tableNumber: state.tableNumber,
      items: state.cart,
      subtotal,
      tax,
      total,
      customerName,
      notes: orderNotes,
    };

    actions.addOrder(orderData);
    actions.clearCart();
    setShowCheckoutModal(false);
    setCustomerName("");
    setOrderNotes("");

    alert("تم إرسال طلبك بنجاح! سيتم تحضيره قريباً.");
  };

  const filteredMenuData = getFilteredMenuData();

  return (
    <div className="app-container">
      <div className="app-header">
        <div className="header-content">
          <div className="header-text">
            <h1>{t.appName}</h1>
            <p>{t.tagline}</p>
          </div>

          <div className="header-actions">
            {/* زر تبديل اللغة */}
            <button
              className="language-btn"
              onClick={() => {
                const newLang = currentLanguage === "ar" ? "en" : "ar";
                localStorage.setItem("preferred-language", newLang);
                window.location.reload();
              }}
              title="تغيير اللغة / Change Language"
            >
              🌍 {currentLanguage === "ar" ? "English" : "عربي"}
            </button>

            {/* زر تحديث القائمة */}
            <button
              className={`refresh-btn ${refreshing ? "refreshing" : ""}`}
              onClick={handleRefresh}
              disabled={refreshing}
              title={t.refreshMenu}
            >
              <FontAwesomeIcon icon={faSync} spin={refreshing} />
              {refreshing ? t.refreshing : t.refreshMenu}
            </button>
          </div>
        </div>

        {state.tableNumber && (
          <div className="table-indicator">
            <FontAwesomeIcon icon={faTable} />
            {t.tableNumber} {state.tableNumber}
          </div>
        )}

        {!state.tableNumber && (
          <div className="table-selector">
            <FontAwesomeIcon icon={faQrcode} />
            <p>{t.scanQR}</p>
            <select
              onChange={(e) => actions.setTableNumber(parseInt(e.target.value))}
              defaultValue=""
            >
              <option value="">{t.selectTable}</option>
              {Array.from({ length: 20 }, (_, i) => (
                <option key={i + 1} value={i + 1}>
                  {t.tableNumber} {i + 1}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      <div className="content-container">
        <div className="menu-container">
          {loadingCategories ? (
            <div className="loading-categories">
              <p>{t.loadingCategories}</p>
            </div>
          ) : (
            <CategoryTabs
              categories={[
                t.allCategories,
                ...categories
                  .sort(
                    (a, b) => (a.display_order || 0) - (b.display_order || 0)
                  )
                  .map((cat) => cat.name),
              ]}
              activeCategory={activeCategory}
              setActiveCategory={setActiveCategory}
            />
          )}

          <div className="menu-sections">
            {filteredMenuData.map((section) => (
              <section key={section.category} className="menu-section">
                <h2>{section.category}</h2>
                <div className="menu-items">
                  {section.items.map((item) => (
                    <MenuItem
                      key={item.id}
                      item={item}
                      addToCart={handleAddToCart}
                    />
                  ))}
                </div>
              </section>
            ))}
          </div>
        </div>

        <Cart
          cart={state.cart}
          updateQty={handleUpdateCartQty}
          removeItem={handleRemoveFromCart}
          onCheckout={handleCheckout}
        />
      </div>

      {showCheckoutModal && (
        <div
          className="checkout-modal-overlay"
          onClick={() => setShowCheckoutModal(false)}
        >
          <div className="checkout-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{t.confirmOrder}</h2>
              <button
                className="close-btn"
                onClick={() => setShowCheckoutModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="order-summary">
                <h3>
                  {t.orderSummary} - {t.tableNumber} {state.tableNumber}
                </h3>
                {state.cart.map((item) => (
                  <div key={item.id} className="order-item">
                    <span>
                      {item.qty}x {item.name}
                    </span>
                    <span>
                      {(item.qty * item.price).toFixed(2)} {t.currency}
                    </span>
                  </div>
                ))}

                <div className="order-totals">
                  <div className="total-row">
                    <span>{t.subtotal}</span>
                    <span>
                      {state.cart
                        .reduce((sum, item) => sum + item.qty * item.price, 0)
                        .toFixed(2)}{" "}
                      {t.currency}
                    </span>
                  </div>
                  <div className="total-row">
                    <span>{t.tax}</span>
                    <span>
                      {(
                        state.cart.reduce(
                          (sum, item) => sum + item.qty * item.price,
                          0
                        ) * 0.15
                      ).toFixed(2)}{" "}
                      {t.currency}
                    </span>
                  </div>
                  <div className="total-row final-total">
                    <span>{t.total}</span>
                    <span>
                      {(
                        state.cart.reduce(
                          (sum, item) => sum + item.qty * item.price,
                          0
                        ) * 1.15
                      ).toFixed(2)}{" "}
                      {t.currency}
                    </span>
                  </div>
                </div>
              </div>

              <div className="customer-info">
                <div className="form-group">
                  <label>{t.customerName}</label>
                  <input
                    type="text"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    placeholder={t.customerNamePlaceholder}
                  />
                </div>

                <div className="form-group">
                  <label>{t.orderNotes}</label>
                  <textarea
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    placeholder={t.orderNotesPlaceholder}
                    rows="3"
                  />
                </div>
              </div>

              <div className="modal-actions">
                <button className="confirm-order-btn" onClick={confirmOrder}>
                  {t.confirm}
                </button>
                <button
                  className="cancel-btn"
                  onClick={() => setShowCheckoutModal(false)}
                >
                  {t.cancel}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
