-- فحص حالة جدول orders وأنواع البيانات

-- 1. التحقق من بنية الجدول
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- 2. التحقق من البيانات الموجودة
SELECT 
    id,
    table_number,
    customer_name,
    subtotal,
    tax,
    total,
    status,
    created_at,
    -- فحص أنواع البيانات الفعلية
    pg_typeof(table_number) as table_number_type,
    pg_typeof(subtotal) as subtotal_type,
    pg_typeof(tax) as tax_type,
    pg_typeof(total) as total_type
FROM "public"."orders" 
ORDER BY created_at DESC;

-- 3. التحقق من القيود (Constraints)
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'public.orders'::regclass;

-- 4. التحقق من الفهارس
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'orders' 
AND schemaname = 'public';

-- 5. عدد السجلات في الجدول
SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders,
    COUNT(CASE WHEN status = 'preparing' THEN 1 END) as preparing_orders,
    COUNT(CASE WHEN status = 'ready' THEN 1 END) as ready_orders,
    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders
FROM "public"."orders";

-- 6. التحقق من السجلات المشكوك فيها
SELECT 
    id,
    table_number,
    subtotal,
    tax,
    total,
    'مشكلة في table_number' as issue
FROM "public"."orders" 
WHERE pg_typeof(table_number) != 'integer'::regtype

UNION ALL

SELECT 
    id,
    table_number,
    subtotal,
    tax,
    total,
    'مشكلة في subtotal' as issue
FROM "public"."orders" 
WHERE pg_typeof(subtotal) NOT IN ('numeric'::regtype, 'double precision'::regtype)

UNION ALL

SELECT 
    id,
    table_number,
    subtotal,
    tax,
    total,
    'مشكلة في tax' as issue
FROM "public"."orders" 
WHERE pg_typeof(tax) NOT IN ('numeric'::regtype, 'double precision'::regtype)

UNION ALL

SELECT 
    id,
    table_number,
    subtotal,
    tax,
    total,
    'مشكلة في total' as issue
FROM "public"."orders" 
WHERE pg_typeof(total) NOT IN ('numeric'::regtype, 'double precision'::regtype);

-- 7. اقتراحات للإصلاح
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' 
            AND column_name = 'table_number' 
            AND data_type = 'character varying'
        ) THEN 'يجب تحويل table_number من VARCHAR إلى INTEGER'
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' 
            AND column_name = 'subtotal' 
            AND data_type = 'character varying'
        ) THEN 'يجب تحويل subtotal من VARCHAR إلى NUMERIC'
        ELSE 'أنواع البيانات تبدو صحيحة'
    END as recommendation;
