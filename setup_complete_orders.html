<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جدول الطلبات الشامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .step h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left: 5px solid #FFC107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left: 5px solid #4CAF50;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border-left: 5px solid #f44336;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        #output {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗃️ إعداد جدول الطلبات الشامل</h1>
        
        <div class="warning">
            <h3>⚠️ تنبيه مهم</h3>
            <p>هذه الأداة ستنشئ جدول <code>complete_orders</code> الجديد في قاعدة البيانات. تأكد من أن لديك صلاحيات الإدارة.</p>
        </div>

        <div class="step">
            <h3>📋 الخطوة 1: التحقق من الاتصال</h3>
            <p>تأكد من أن التطبيق يعمل وأن Supabase متصل</p>
            <button class="button" onclick="checkConnection()">🔍 فحص الاتصال</button>
        </div>

        <div class="step">
            <h3>🔧 الخطوة 2: إنشاء الجدول</h3>
            <p>إنشاء جدول <code>complete_orders</code> مع جميع الحقول المطلوبة</p>
            <button class="button" onclick="createTable()">🏗️ إنشاء الجدول</button>
        </div>

        <div class="step">
            <h3>📦 الخطوة 3: إضافة بيانات تجريبية</h3>
            <p>إضافة طلبات تجريبية لاختبار النظام</p>
            <button class="button" onclick="addSampleData()">📊 إضافة بيانات تجريبية</button>
        </div>

        <div class="step">
            <h3>✅ الخطوة 4: التحقق من النتائج</h3>
            <p>التحقق من أن الجدول تم إنشاؤه والبيانات تم إدراجها بنجاح</p>
            <button class="button" onclick="verifySetup()">🔍 التحقق من الإعداد</button>
        </div>

        <div class="step">
            <h3>🚀 الخطوة 5: تحديث التطبيق</h3>
            <p>إعادة تشغيل التطبيق لاستخدام الجدول الجديد</p>
            <button class="button" onclick="refreshApp()">🔄 إعادة تحميل التطبيق</button>
        </div>

        <div id="output"></div>

        <div class="success" style="display: none;" id="successMessage">
            <h3>🎉 تم الإعداد بنجاح!</h3>
            <p>جدول <code>complete_orders</code> جاهز للاستخدام. الآن ستظهر أسماء المنتجات في صفحات النادل والباريستا!</p>
        </div>
    </div>

    <script>
        let output = document.getElementById('output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00bcd4',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            
            output.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">
                [${timestamp}] ${message}
            </div>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        async function checkConnection() {
            log('🔍 فحص الاتصال مع Supabase...', 'info');
            
            try {
                // محاولة الوصول لجدول موجود
                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/products?select=count', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                    }
                });
                
                if (response.ok) {
                    log('✅ الاتصال مع Supabase يعمل بنجاح!', 'success');
                    return true;
                } else {
                    log('❌ فشل الاتصال مع Supabase: ' + response.status, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في الاتصال: ' + error.message, 'error');
                return false;
            }
        }

        async function createTable() {
            log('🔧 إنشاء جدول complete_orders...', 'info');
            
            try {
                // محاولة إدراج سجل تجريبي لإنشاء الجدول
                const testRecord = {
                    order_id: '00000000-0000-0000-0000-000000000000',
                    table_number: 999,
                    customer_name: 'Test Customer',
                    notes: 'Test record to create table',
                    product_name: 'Test Product',
                    unit_price: 0.01,
                    quantity: 1,
                    status: 'test'
                };

                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/complete_orders', {
                    method: 'POST',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testRecord)
                });

                if (response.ok) {
                    log('✅ تم إنشاء الجدول بنجاح!', 'success');
                    
                    // حذف السجل التجريبي
                    await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/complete_orders?order_id=eq.00000000-0000-0000-0000-000000000000', {
                        method: 'DELETE',
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                        }
                    });
                    
                    log('🧹 تم تنظيف السجل التجريبي', 'info');
                    return true;
                } else {
                    const errorText = await response.text();
                    log('❌ فشل إنشاء الجدول: ' + errorText, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في إنشاء الجدول: ' + error.message, 'error');
                return false;
            }
        }

        async function addSampleData() {
            log('📦 إضافة بيانات تجريبية...', 'info');
            
            const sampleData = [
                {
                    order_id: '11111111-1111-1111-1111-111111111111',
                    table_number: 5,
                    customer_name: 'أحمد محمد',
                    notes: 'بدون سكر، قهوة قوية',
                    product_name: 'قهوة تركية',
                    product_description: 'قهوة بن عربي أصيلة مع الهيل',
                    unit_price: 15.00,
                    quantity: 2,
                    status: 'pending',
                    assigned_waiter: 'سالم أحمد',
                    twitter_handle: '@ahmed_coffee'
                },
                {
                    order_id: '11111111-1111-1111-1111-111111111111',
                    table_number: 5,
                    customer_name: 'أحمد محمد',
                    notes: 'بدون سكر، قهوة قوية',
                    product_name: 'كابتشينو',
                    product_description: 'إسبريسو مع حليب مبخر ورغوة',
                    unit_price: 18.00,
                    quantity: 1,
                    status: 'pending',
                    assigned_waiter: 'سالم أحمد',
                    twitter_handle: '@ahmed_coffee'
                },
                {
                    order_id: '22222222-2222-2222-2222-222222222222',
                    table_number: 3,
                    customer_name: 'فاطمة علي',
                    notes: 'سكر خفيف، حليب إضافي',
                    product_name: 'لاتيه',
                    product_description: 'إسبريسو مع حليب ساخن وطبقة رغوة',
                    unit_price: 20.00,
                    quantity: 1,
                    status: 'confirmed',
                    assigned_waiter: 'محمد سعد',
                    twitter_handle: '@fatima_latte'
                }
            ];

            try {
                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/complete_orders', {
                    method: 'POST',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(sampleData)
                });

                if (response.ok) {
                    const result = await response.json();
                    log(`✅ تم إدراج ${result.length} سجل بنجاح!`, 'success');
                    return true;
                } else {
                    const errorText = await response.text();
                    log('❌ فشل إدراج البيانات: ' + errorText, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في إدراج البيانات: ' + error.message, 'error');
                return false;
            }
        }

        async function verifySetup() {
            log('🔍 التحقق من الإعداد...', 'info');
            
            try {
                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/complete_orders?select=*&limit=5', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ الجدول يعمل بنجاح! يحتوي على ${data.length} سجل`, 'success');
                    
                    data.forEach((record, index) => {
                        log(`📋 طلب ${index + 1}: ${record.product_name} - طاولة ${record.table_number} - ${record.customer_name}`, 'info');
                    });
                    
                    document.getElementById('successMessage').style.display = 'block';
                    return true;
                } else {
                    log('❌ فشل التحقق من الجدول: ' + response.status, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في التحقق: ' + error.message, 'error');
                return false;
            }
        }

        function refreshApp() {
            log('🔄 إعادة تحميل التطبيق...', 'info');
            setTimeout(() => {
                window.location.href = 'http://localhost:3001';
            }, 1000);
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 مرحباً بك في أداة إعداد جدول الطلبات الشامل', 'success');
            log('💡 اتبع الخطوات بالترتيب لإعداد النظام', 'info');
        };
    </script>
</body>
</html>
