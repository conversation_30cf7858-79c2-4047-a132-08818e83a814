/* نظام ألوان Avie المحسن - مقهى عصري */
:root {
  /* الألوان الأساسية */
  --primary-color: #2c5530; /* أخضر داكن طبيعي */
  --secondary-color: #a4b494; /* أخضر فاتح مريح */
  --accent-color: #d4a574; /* ذهبي دافئ (لون القهوة) */
  --background-color: #f7f5f3; /* كريمي فاتح */
  --text-dark: #2d2d2d; /* رمادي داكن */
  --text-light: #ffffff; /* أبيض نقي */

  /* ألوان النصوص والعناوين */
  --headline-color: #2c5530; /* عناوين خضراء داكنة */
  --paragraph-color: #2d2d2d; /* فقرات رمادية داكنة */
  --text-color: #2d2d2d; /* نص عام */

  /* ألوان الأزرار */
  --button-color: #d4a574; /* أزرار ذهبية */
  --button-text-color: #ffffff; /* نص الأزرار أبيض */
  --button-hover: #c19660; /* لون الأزرار عند hover */

  /* ألوان مساعدة */
  --success-color: #4caf50; /* أخضر نجاح */
  --warning-color: #ff9800; /* برتقالي تحذير */
  --danger-color: #f44336; /* أحمر خطأ */
  --info-color: #2196f3; /* أزرق معلومات */

  /* ألوان إضافية */
  --white: #ffffff;
  --light-gray: #f5f5f5;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(44, 85, 48, 0.1);
  --shadow-medium: 0 4px 16px rgba(44, 85, 48, 0.15);
  --shadow-heavy: 0 8px 32px rgba(44, 85, 48, 0.2);
  --transition: all 0.3s ease;

  /* تدرجات جميلة */
  --gradient-primary: linear-gradient(135deg, #2c5530, #a4b494);
  --gradient-accent: linear-gradient(135deg, #d4a574, #c19660);
  --gradient-background: linear-gradient(135deg, #f7f5f3, #ffffff);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "almarai", sans-serif;
  text-decoration: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
}

/* إزالة جميع الخطوط من العناصر */
label,
span,
p,
h1,
h2,
h3,
h4,
h5,
h6,
button,
input,
textarea,
select,
div {
  text-decoration: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
  text-underline-offset: none !important;
  text-decoration-color: transparent !important;
  text-decoration-style: none !important;
}

/* إزالة الخطوط من pseudo-elements */
*::before,
*::after,
label::before,
label::after,
span::before,
span::after,
p::before,
p::after,
h1::before,
h1::after,
h2::before,
h2::after,
h3::before,
h3::after,
h4::before,
h4::after,
h5::before,
h5::after,
h6::before,
h6::after,
button::before,
button::after,
input::before,
input::after,
textarea::before,
textarea::after,
select::before,
select::after,
div::before,
div::after {
  text-decoration: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
  text-underline-offset: none !important;
  text-decoration-color: transparent !important;
  text-decoration-style: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background-image: none !important;
  content: none !important;
}

body {
  background: var(--gradient-background);
  color: var(--text-color);
  min-height: 100vh;
  line-height: 1.6;
  direction: rtl;
  font-family: "almarai", sans-serif;
}

.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--secondary-color);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: 1rem;
}

.header-text {
  flex: 1;
}

.refresh-btn {
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a) !important;
  color: white !important;
  border: 3px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 15px !important;
  padding: 0.8rem 1.5rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.2) !important;
  min-width: 160px !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d5a5a, var(--primary-color));
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(0, 70, 67, 0.4);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn.refreshing {
  background: linear-gradient(135deg, #2d5a5a, var(--primary-color));
  border-color: rgba(255, 255, 255, 0.5);
}

.app-header h1 {
  text-align: center;
  color: var(--headline-color);
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.content-container {
  text-align: center;
  display: flex;
  gap: 30px;
  flex-direction: column;
}

.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
  scrollbar-width: thin;
}

.category-tabs::-webkit-scrollbar {
  height: 5px;
}

.category-tabs::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 10px;
}

.category-tab {
  background: none;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  white-space: nowrap;
  transition: var(--transition);
  font-size: 14px;
  border: 1px solid var(--secondary-color);
}

.category-tab.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.menu-section {
  margin-bottom: 40px;
}

.menu-section h2 {
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--secondary-color);
  position: relative;
}

.menu-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  justify-items: stretch;
}

/* Menu Item Styles - Simple and Elegant Card Design */
.menu-item {
  width: 260px;
  height: auto;
  padding: 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  position: relative;
  display: flex;
  flex-direction: column;
  border: 2px solid var(--border-color);
}

.menu-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.item-image {
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-item:hover .item-image img {
  transform: scale(1.05);
}

.item-details {
  flex-grow: 1;
  text-align: center;
}

.item-details h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
}

.item-details p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  text-align: center;
}

.item-price {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.quantity-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.quantity-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.qty-input {
  width: 70px;
  padding: 8px;
  text-align: center;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  align-self: flex-end;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.qty-input:focus {
  outline: none;
  border-color: #004643;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(0, 70, 67, 0.1);
}

.add-to-cart-btn {
  background-color: #004643;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 14px;
  font-weight: 500;
}

.add-to-cart-btn:hover {
  background-color: #003530;
  transform: translateY(-1px);
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

.add-to-cart-btn.adding {
  background-color: #28a745;
  transform: scale(0.95);
}

/* تحسينات إضافية للكروت */
.menu-item {
  border: 1px solid #f0f0f0;
}

.menu-item:hover {
  border-color: #004643;
}

/* تحسين عرض النص في السلة */
.cart-item-info h4 {
  font-size: 1rem;
  line-height: 1.3;
  margin-bottom: 4px;
}

.cart-item .item-price {
  font-size: 0.85rem;
  color: #666;
}

.item-total {
  font-size: 1rem;
  font-weight: bold;
  color: var(--primary-color);
}

/* تنسيق responsive للكروت */
@media (max-width: 480px) {
  .menu-items {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .menu-item {
    width: 100%;
    max-width: 280px;
  }
}

/* تصميم صفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #004643 0%, #abd1c6 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  text-align: center;
}

.login-header {
  margin-bottom: 30px;
}

.cafe-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  text-shadow: 0 2px 4px rgba(0, 70, 67, 0.3);
}

/* تصميم شعار Avie - محسن للصورة من assets */
.avie-logo {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.avie-logo:hover {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  transform: scale(1.02);
}

.cafe-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 10px;
}

/* تصميم خاص للشعار في الصفحات المختلفة */
.customer-hero .cafe-logo {
  margin-bottom: 30px;
}

.login-header .cafe-logo {
  margin-bottom: 20px;
}

.navbar-brand .avie-logo {
  filter: brightness(1.2) drop-shadow(0 2px 4px rgba(238, 255, 224, 0.4));
}

/* تحسينات إضافية للشعار */
.avie-logo {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* تأثيرات خاصة للشعار في الصفحات المختلفة */
.admin-header .avie-logo,
.customer-hero .avie-logo,
.login-header .avie-logo {
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

.admin-header .avie-logo:hover,
.customer-hero .avie-logo:hover,
.login-header .avie-logo:hover {
  filter: drop-shadow(0 6px 20px rgba(0, 0, 0, 0.25));
  transform: scale(1.05);
}

/* تصميم خاص لاسم Avie */
.cafe-logo h1 {
  font-family: "Arial", sans-serif;
  font-weight: 700;
  letter-spacing: 2px;
  text-transform: uppercase;
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.login-header h1 {
  color: var(--primary-color);
  font-size: 2rem;
  margin: 0;
}

.login-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.login-form {
  text-align: right;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  right: 15px;
  color: #666;
  z-index: 2;
}

.input-wrapper input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: white;
  box-shadow: 0 0 0 3px rgba(0, 70, 67, 0.1);
}

.password-toggle {
  position: absolute;
  left: 15px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 5px;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.user-type-selector {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.user-type-option {
  cursor: pointer;
}

.user-type-option input[type="radio"] {
  display: none;
}

.user-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 100px;
}

.user-type-option input[type="radio"]:checked + .user-type-card {
  border-color: var(--primary-color);
  background-color: rgba(0, 70, 67, 0.1);
  color: var(--primary-color);
}

.user-type-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.user-type-card svg {
  font-size: 1.5rem;
}

.login-btn {
  width: 100%;
  padding: 15px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.login-btn:hover:not(:disabled) {
  background-color: #003530;
  transform: translateY(-2px);
}

.login-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.toggle-form-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: underline;
  font-size: 14px;
}

.toggle-form-btn:hover {
  color: #003530;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
  text-align: center;
}

.demo-info {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.demo-info h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1rem;
}

.demo-accounts {
  text-align: right;
  font-size: 14px;
  line-height: 1.6;
}

.demo-accounts div {
  margin-bottom: 5px;
  color: #666;
}

/* تصميم شريط التنقل */
.navbar {
  background: #004643;
  color: var(--text-light);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-medium);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.3rem;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #eeffe0;
}

.navbar-brand .avie-logo {
  filter: brightness(1.2) drop-shadow(0 2px 4px rgba(238, 255, 224, 0.3));
}

.brand-icon {
  font-size: 1.8rem;
  color: #eeffe0;
  text-shadow: 0 2px 4px rgba(238, 255, 224, 0.3);
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-type-icon {
  font-size: 1.2rem;
  color: #eeffe0;
}

.user-details {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.user-name {
  font-weight: bold;
  font-size: 1rem;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
  color: #eeffe0;
}

/* تصميم صفحة عدم الصلاحية */
.access-denied {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 20px;
}

.access-denied-card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.access-denied-card h2 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.access-denied-card p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.access-denied-card strong {
  color: var(--primary-color);
}

/* تحسينات إضافية للتطبيق */
.app {
  min-height: 100vh;
  background-color: #f9f5f1;
}

/* تصميم لوحة تحكم الأدمن */
.admin-dashboard {
  min-height: calc(100vh - 70px);
  background-color: #f9f5f1;
}

.admin-tabs {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0 20px;
  padding-top: 1rem;
  padding-bottom: 1rem;
  display: flex;
  gap: 0;
  overflow-x: auto;
}

.admin-tab {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.admin-tab:hover {
  color: var(--primary-color);
  background-color: rgba(0, 70, 67, 0.05);
}

.admin-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: rgba(0, 70, 67, 0.1);
}

.admin-tab svg {
  font-size: 16px;
}

.admin-content {
  padding: 0;
  background-color: #f9f5f1;
  min-height: calc(100vh - 140px);
}

/* تحسينات responsive للوحة الأدمن */
@media (max-width: 768px) {
  .admin-tabs {
    padding: 0 10px;
    overflow-x: auto;
    scrollbar-width: thin;
  }

  .admin-tabs::-webkit-scrollbar {
    height: 3px;
  }

  .admin-tabs::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 10px;
  }

  .admin-tab {
    padding: 12px 20px;
    font-size: 13px;
  }

  .admin-tab span {
    display: none;
  }

  .admin-tab svg {
    font-size: 18px;
  }
}

/* تنسيق responsive للباريستا - الأجهزة اللوحية */
@media (max-width: 1024px) {
  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.2rem;
  }

  .barista-header {
    padding: 1.2rem;
  }
}

/* تنسيق responsive لشريط التنقل */
@media (max-width: 768px) {
  .navbar {
    padding: 10px 15px;
    flex-direction: column;
    gap: 15px;
  }

  .navbar-user {
    width: 100%;
    justify-content: space-between;
  }

  .user-details {
    text-align: left;
  }

  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .user-type-selector {
    flex-direction: column;
    gap: 10px;
  }

  .user-type-card {
    min-width: auto;
    width: 100%;
  }
}

/* Category Section Styles */
.menu-category-section {
  margin-bottom: 1.5rem;
}

.category-header {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: var(--secondary-color);
  border-radius: 8px;
}

.category-title {
  color: var(--headline-color);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-items {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 0.5rem;
  }

  .menu-item {
    width: 100%;
    max-width: 260px;
    min-height: 260px;
  }

  .item-image {
    width: 70px;
    height: 70px;
  }

  .item-details h3 {
    font-size: 1.1rem;
  }

  .item-details p {
    font-size: 0.85rem;
  }

  .item-price {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .menu-items {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    padding: 0.25rem;
  }
}

@media (max-width: 360px) {
  .menu-items {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 0.25rem;
  }

  .menu-item {
    width: 100%;
    max-width: none;
    padding: 12px;
    min-height: 240px;
  }

  .item-image {
    width: 60px;
    height: 60px;
  }

  .item-details h3 {
    font-size: 1rem;
  }

  .item-details p {
    font-size: 0.8rem;
  }

  .item-price {
    font-size: 1.1rem;
  }

  .qty-btn {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
}

.cart-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 320px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: var(--transition);
  border: 1px solid #e9ecef;
}

.cart-header {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border-radius: 12px 12px 0 0;
}

.cart-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cart-count {
  background-color: var(--white);
  color: var(--primary-color);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.cart-content {
  padding: 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.cart-content::-webkit-scrollbar {
  width: 6px;
}

.cart-content::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 10px;
}

.cart-content::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 10px;
}

.empty-cart {
  text-align: center;
  padding: 20px;
  color: var(--paragraph-color);
}

.cart-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cart-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.cart-item-info h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  text-align: right;
}

.cart-item-info .item-price {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.cart-item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.item-info {
  display: flex;
  align-items: center;
}

.remove-item {
  background-color: #e74c3c;
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 8px;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-controls input {
  width: 50px;
  padding: 5px;
  text-align: center;
  border: 1px solid var(--secondary-color);
  border-radius: 4px;
}

.item-total {
  color: var(--primary-color);
  font-weight: bold;
  min-width: 60px;
  text-align: left;
}

/* تحسين أزرار التحكم في الكمية في السلة */
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
}

.qty-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.qty-btn:hover:not(:disabled) {
  background-color: #003530;
  transform: scale(1.05);
}

.qty-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.qty-display {
  min-width: 30px;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.remove-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background-color: #c0392b;
  transform: scale(1.05);
}

.cart-summary {
  padding: 15px 0;
  border-top: 1px solid var(--secondary-color);
  margin-top: 10px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-row.total {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 15px 0;
  color: var(--primary-color);
}

.checkout-btn {
  width: 100%;
  padding: 12px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: var(--transition);
}

.checkout-btn:hover {
  background-color: var(--headline-color);
}

/* تنسيقات الفئات والبطاقات المميزة */
.premium {
  border: 2px solid #d4af37;
  position: relative;
}

.popular-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #ff6b6b;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.premium-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #d4af37;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: bold;
}

.premium-btn {
  background-color: #d4af37 !important;
  color: #2c3e50 !important;
  font-weight: bold !important;
}

.premium-btn:hover {
  background-color: #c9a227 !important;
}

@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }

  .cart-container {
    position: static;
    width: 100%;
    margin-top: 20px;
  }

  .menu-items {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}
/* تنسيقات الفئات والبطاقات المميزة */
.premium {
  border: 2px solid #d4af37;
  position: relative;
}

.popular-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #ff6b6b;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.premium-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #d4af37;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: bold;
}

.premium-btn {
  background-color: #d4af37 !important;
  color: #2c3e50 !important;
  font-weight: bold !important;
}

.premium-btn:hover {
  background-color: #c9a227 !important;
}

/* تحسينات التصنيفات */
.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
  scrollbar-width: thin;
}

.category-tabs::-webkit-scrollbar {
  height: 5px;
}

.category-tabs::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 10px;
}

.category-tab {
  background: none;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  white-space: nowrap;
  transition: var(--transition);
  font-size: 14px;
  border: 1px solid var(--secondary-color);
}

.category-tab.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* تأثيرات البطاقات */
.menu-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.menu-item.hovered {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* تحسينات كمية المنتج */
.qty-input {
  width: 60px;
  padding: 8px;
  text-align: center;
  border: 1px solid var(--secondary-color);
  border-radius: 5px;
  font-size: 14px;
}

.qty-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Navigation Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-nav {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow);
}

.nav-brand h2 {
  margin: 0;
  color: var(--white);
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  color: var(--white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: var(--transition);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
}

.main-content {
  flex: 1;
  padding: 2rem;
}

/* Table Indicator and Selector */
.table-indicator {
  background-color: var(--secondary-color);
  color: var(--primary-color);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  font-weight: bold;
  margin: 1rem 0;
}

.table-selector {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin: 1rem 0;
}

.table-selector select {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--secondary-color);
  border-radius: 5px;
  font-size: 1rem;
}

/* Modal Styles */
.checkout-modal-overlay,
.order-modal-overlay,
.payment-modal-overlay,
.qr-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.checkout-modal,
.order-modal,
.payment-modal,
.qr-modal {
  background-color: var(--white);
  border-radius: 20px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: var(--white);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  color: white;
  font-size: 1.4rem;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.modal-content {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Waiter Order Details Modal Styles */
.order-modal .order-details {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.order-modal .order-details p {
  margin: 0.75rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  border: 1px solid #e9ecef;
  font-size: 1rem;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-modal .order-details strong {
  color: var(--primary-color);
  font-weight: 600;
  min-width: 120px;
  flex-shrink: 0;
}

.order-modal .order-items-detail {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.order-modal .order-items-detail h3 {
  color: var(--primary-color);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-modal .item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  margin-bottom: 1rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-modal .item-detail:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
  background: white;
}

.order-modal .item-detail:last-child {
  margin-bottom: 0;
}

.order-modal .item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-modal .item-name {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.order-modal .item-desc {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.order-modal .item-qty-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  min-width: 100px;
}

.order-modal .qty {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9rem;
}

.order-modal .price {
  font-weight: bold;
  color: #28a745;
  font-size: 1.1rem;
}

.order-modal .order-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.order-modal .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
  font-size: 1rem;
}

.order-modal .summary-row:last-child {
  border-bottom: none;
}

.order-modal .summary-row.total {
  font-weight: bold;
  font-size: 1.2rem;
  color: var(--primary-color);
  border-top: 2px solid var(--primary-color);
  margin-top: 0.5rem;
  padding-top: 1rem;
  background: white;
  border-radius: 10px;
  padding: 1rem;
  margin: 0.5rem -0.5rem 0 -0.5rem;
}

.order-modal .modal-actions {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 20px 20px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.order-modal .collect-payment-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.order-modal .collect-payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.order-summary h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--secondary-color);
}

.order-totals {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid var(--secondary-color);
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.final-total {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary-color);
  border-top: 1px solid var(--primary-color);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.customer-info {
  margin: 1.5rem 0;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: 5px;
  font-size: 1rem;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.confirm-order-btn,
.confirm-payment-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: var(--transition);
}

.confirm-order-btn:hover,
.confirm-payment-btn:hover {
  background-color: #5a3c2e;
}

.cancel-btn {
  background-color: #6c757d;
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: var(--transition);
}

.cancel-btn:hover {
  background-color: #5a6268;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 10000;
  animation: slideInRight 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 400px;
}

.notification.success {
  background: linear-gradient(135deg, #e16162, #e16162);
}

.notification.error {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.notification.warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #212529;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Barista Dashboard Styles */
.barista-container {
  width: 100%;
  min-height: calc(100vh - 70px);
  padding: 20px;
  background-color: #f9f5f1;
}

.barista-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #004643 0%, #004643 100%);
  border-radius: 15px;
  color: white;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
}

.barista-header h1 {
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.notification-badge {
  background-color: #ff6b6b;
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: bold;
  animation: pulse 2s infinite;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.action-buttons-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.secondary-buttons-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  padding: 0.75rem 1.2rem !important;
  border-radius: 8px !important;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px !important;
  font-weight: 500;
  width: auto !important;
  height: auto !important;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  color: white;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2) !important;
}

.add-order-btn {
  background: rgba(194, 192, 67, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.add-order-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.add-order-btn svg {
  font-size: 16px;
}

/* تصميم الأزرار المختلفة */
.primary-btn {
  background: rgba(76, 175, 80, 0.2) !important;
  border-color: rgba(76, 175, 80, 0.4) !important;
  color: white !important;
}

.primary-btn:hover {
  background: rgba(76, 175, 80, 0.4) !important;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  font-size: 13px !important;
  padding: 0.6rem 1rem !important;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.25) !important;
}

.danger-btn {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: rgba(244, 67, 54, 0.4) !important;
  color: white !important;
  font-size: 12px !important;
  padding: 0.5rem 0.75rem !important;
}

.danger-btn:hover {
  background: rgba(244, 67, 54, 0.4) !important;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
}

/* New Order Modal Styles */
.new-order-modal {
  background: white;
  border-radius: 16px;
  max-width: 1400px;
  width: 98%;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  position: relative;
}

.modal-header {
  background: linear-gradient(135deg, #004643 0%, #006b5d 50%, #abd1c6 100%);
  color: white;
  padding: 1.25rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-header h2::before {
  content: "➕";
  font-size: 1.2rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.modal-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main-content-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  background: #f8f9fa;
}

.order-info-section {
  padding: 1.5rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.order-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.order-form-grid .form-group:last-child {
  grid-column: 1 / -1;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: var(--transition);
  background: white;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 70, 67, 0.1);
}

.menu-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.menu-header {
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.menu-header h3 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.menu-header h3::before {
  content: "🍽️";
  font-size: 1rem;
}

.menu-categories {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  -webkit-overflow-scrolling: touch;
}

.menu-categories::-webkit-scrollbar,
.cart-content::-webkit-scrollbar {
  width: 6px;
}

.menu-categories::-webkit-scrollbar-track,
.cart-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.menu-categories::-webkit-scrollbar-thumb,
.cart-content::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

.menu-categories::-webkit-scrollbar-thumb:hover,
.cart-content::-webkit-scrollbar-thumb:hover {
  background: #003a37;
}

.category-section {
  margin-bottom: 1.5rem;
}

.category-section:last-child {
  margin-bottom: 0;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f9bc60;
  color: white;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  font-size: 0.95rem;
}

.category-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.items-grid {
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
  padding: 1rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  justify-content: center;
}

.menu-item-card {
  display: flex;
  flex-direction: row;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: var(--transition);
  cursor: pointer;
  overflow: hidden;
  position: relative;
  height: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.menu-item-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.12);
  transform: translateY(-1px);
}

.menu-item-image {
  width: 100px;
  height: 80%;
  object-fit: cover;
  border-radius: 0;
  flex-shrink: 0;
}

.item-content {
  padding: 0.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  order: -1;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.item-info h5 {
  margin: 0 0 0.4rem 0;
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  padding-top: 20px;
}

.item-info p {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.75rem;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.item-price {
  color: #001e1d;
  font-weight: bold;
  font-size: 1rem;
}

.add-item-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 70, 67, 0.3);
  white-space: nowrap;
}

.add-item-btn:hover {
  background: #003a37;
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(0, 70, 67, 0.4);
}

/* Product Badges */
.product-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-size: 0.6rem;
  font-weight: 600;
  color: white;
  z-index: 2;
}

.badge-popular {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.badge-premium {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
}

.cart-section {
  display: flex;
  flex-direction: column;
  background: white;
  border-top: 2px solid #e9ecef;
  flex-shrink: 0;
}

.cart-header {
  padding: 0.75rem 1rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: var(--transition);
}

.cart-header:hover {
  background: #f8f9fa;
}

.cart-header h3 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cart-header h3::before {
  content: "🛒";
  font-size: 1rem;
}

.cart-toggle-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.cart-toggle-btn:hover {
  background: rgba(0, 70, 67, 0.1);
}

.cart-section.collapsed .cart-content {
  display: none;
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem 1rem;
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
}

.cart-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: var(--transition);
}

.cart-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.1);
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: var(--primary-color);
}

.item-price {
  color: #001e1d;
  font-size: 1.1rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-controls button {
  background: var(--primary-color);
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  font-size: 0.8rem;
}

.quantity-controls button:hover {
  background: #003a37;
}

.quantity-controls .remove-btn {
  background: #dc3545;
}

.quantity-controls .remove-btn:hover {
  background: #c82333;
}

.quantity-controls .qty {
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
  border: 1px solid #dee2e6;
  color: var(--primary-color);
  font-size: 0.875rem;
}

.order-total {
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--primary-color);
  margin-top: auto;
  flex-shrink: 0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.final {
  font-weight: bold;
  font-size: 1rem;
  color: var(--primary-color);
  border-top: 2px solid var(--primary-color);
  padding-top: 0.75rem;
  margin-top: 0.5rem;
}

.modal-actions {
  padding: 0.75rem 1rem;
  background: white;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  flex-shrink: 0;
}

.submit-order-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.submit-order-btn:hover:not(:disabled) {
  background: #218838;
}

.submit-order-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.cancel-btn:hover {
  background: #5a6268;
}

/* Empty Cart Styles */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem 1rem;
  background: white;
  border-radius: 12px;
  border: 1px dashed #dee2e6;
  text-align: center;
  height: fit-content;
  min-height: 120px;
}

.empty-cart-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.empty-cart h3 {
  color: #6c757d;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.empty-cart p {
  color: #004643;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* تصميم متجاوب للنموذج الجديد */

@media (max-width: 1024px) {
  .modal-content {
    flex-direction: column;
    height: 100%;
  }

  .cart-section {
    max-height: 30vh;
    overflow-y: auto;
  }

  .order-form-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .items-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    padding: 1rem;
    justify-content: center;
  }

  .menu-item-card {
    height: 110px;
  }

  .menu-item-image {
    width: 90px;
  }

  .item-content {
    padding: 0.75rem;
    gap: 0.3rem;
  }

  .item-info h5 {
    font-size: 0.85rem;
    margin-bottom: 0.4rem;
  }

  .item-info p {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 768px) {
  .new-order-modal {
    width: 98%;
    max-height: 95vh;
    border-radius: 15px;
  }

  .modal-content {
    flex-direction: column;
    height: 100%;
  }

  .cart-section {
    max-height: 35vh;
    overflow-y: auto;
  }

  .cart-section.collapsed {
    max-height: auto;
  }

  .order-info-section {
    padding: 1rem;
  }

  .order-form-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 1rem;
    justify-content: center;
  }

  .menu-item-card {
    height: 100px;
  }

  .menu-item-image {
    width: 80px;
  }

  .item-content {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .item-info h5 {
    font-size: 0.8rem;
    margin-bottom: 0.4rem;
  }

  .item-info p {
    font-size: 0.7rem;
    margin-bottom: 0.5rem;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-header h2 {
    font-size: 1.2rem;
  }

  .menu-header {
    padding: 0.75rem 1rem;
  }

  .cart-header {
    padding: 0.75rem 1rem;
  }

  .menu-categories {
    padding: 0.75rem 1rem;
  }

  .cart-content {
    padding: 0.75rem 1rem;
  }

  .menu-item-card {
    height: 220px;
  }

  .menu-item-image {
    height: 90px;
  }

  .item-content {
    padding: 0.75rem;
    gap: 0.3rem;
  }

  .item-info h5 {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .item-info p {
    font-size: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .item-footer {
    padding-top: 0.3rem;
  }

  .item-price {
    font-size: 0.8rem;
  }

  .add-item-btn {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .cart-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .quantity-controls {
    align-self: flex-end;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }

  .submit-order-btn,
  .cancel-btn {
    width: 100%;
    justify-content: center;
  }

  .empty-cart {
    min-height: 120px;
    padding: 1.5rem 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .empty-cart-icon {
    font-size: 2.5rem;
  }

  .empty-cart h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  .empty-cart p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .new-order-modal {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .modal-header {
    padding: 0.75rem 1rem;
    flex-shrink: 0;
  }

  .modal-header h2 {
    font-size: 1.1rem;
  }

  .modal-content {
    grid-template-rows: 75vh 25vh;
    height: calc(100vh - 60px);
    gap: 0;
  }

  .order-info-section {
    padding: 0.75rem;
  }

  .order-form-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .menu-header {
    padding: 0.5rem 0.75rem;
  }

  .menu-header h3 {
    font-size: 1rem;
  }

  .cart-header {
    padding: 0.5rem 0.75rem;
  }

  .cart-header h3 {
    font-size: 1rem;
  }

  .menu-categories {
    padding: 0.5rem 0.75rem;
    max-height: none;
  }

  .cart-content {
    padding: 0.4rem 0.6rem;
  }

  .cart-header {
    padding: 0.5rem 0.75rem;
  }

  .cart-header h3 {
    font-size: 0.9rem;
  }

  .menu-categories {
    padding: 0.75rem;
  }

  .category-section {
    margin-bottom: 1rem;
  }

  .category-header {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .category-count {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 0.75rem;
    background: white;
    justify-content: center;
  }

  .menu-item-card {
    height: 90px;
    border-radius: 10px;
  }

  .menu-item-image {
    width: 70px;
  }

  .item-content {
    padding: 0.6rem;
    gap: 0.25rem;
  }

  .item-info h5 {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    line-height: 1.2;
  }

  .item-info p {
    font-size: 0.7rem;
    margin-bottom: 0.4rem;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    line-height: 1.3;
  }

  .item-footer {
    padding-top: 0.4rem;
  }

  .item-price {
    font-size: 0.85rem;
  }

  .add-item-btn {
    width: 30px;
    height: 30px;
    font-size: 0.85rem;
  }

  .category-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .form-group label {
    font-size: 0.8rem;
  }

  .form-group input,
  .form-group textarea {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .modal-actions {
    padding: 0.5rem;
    gap: 0.4rem;
    flex-shrink: 0;
    background: white;
    border-top: 1px solid #e9ecef;
  }

  .submit-order-btn,
  .cancel-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
    min-height: 40px;
  }

  .menu-item-card {
    height: 200px;
  }

  .menu-item-image {
    height: 80px;
  }

  .item-content {
    padding: 0.6rem;
    gap: 0.25rem;
  }

  .item-info h5 {
    font-size: 0.8rem;
    margin-bottom: 0.4rem;
  }

  .item-info p {
    font-size: 0.7rem;
    margin-bottom: 0.6rem;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .item-footer {
    padding-top: 0.25rem;
  }

  .item-price {
    font-size: 0.75rem;
  }

  .add-item-btn {
    width: 22px;
    height: 22px;
    font-size: 0.65rem;
  }

  .product-badge {
    top: 4px;
    right: 4px;
    padding: 0.15rem 0.3rem;
    font-size: 0.55rem;
  }
}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 360px) {
  .items-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .menu-item-card {
    height: 80px;
  }

  .menu-item-image {
    width: 60px;
  }

  .item-content {
    padding: 0.5rem;
  }

  .item-info h5 {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .item-info p {
    font-size: 0.65rem;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    margin-bottom: 0.3rem;
  }

  .item-price {
    font-size: 0.75rem;
  }

  .add-item-btn {
    width: 26px;
    height: 26px;
    font-size: 0.75rem;
  }

  .category-header {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .category-count {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
}

.test-btn,
.clear-btn {
  background: rgba(74, 144, 226, 0.1);
  border: 1px solid rgba(74, 144, 226, 0.3);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  white-space: nowrap;
}

.test-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

.clear-btn {
  border-color: rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.clear-btn:hover {
  background: #dc3545;
  color: white;
  transform: translateY(-1px);
}

/* Admin Page Styles - تحسين تصميم إدارة المنتجات */
.admin-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--gradient-background);
  min-height: 100vh;
}

.admin-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 2rem;
  background: #004643;
  border-radius: 25px;
  color: var(--text-light);
  box-shadow: var(--shadow-heavy);
  position: relative;
  overflow: hidden;
}

.admin-header::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.admin-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.admin-header p {
  font-size: 1.3rem;
  opacity: 0.95;
  margin: 0;
  font-weight: 300;
  position: relative;
  z-index: 1;
}

.admin-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.add-product-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: 0 8px 25px rgba(54, 54, 54, 0.3);
  position: relative;
  overflow: hidden;
}

.add-product-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.add-product-btn:hover::before {
  left: 100%;
}

.add-product-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
}

.add-product-btn svg {
  font-size: 1.3rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Modal Styles - تحسين تصميم النموذج */
.add-product-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 25px;
  width: 100%;
  max-width: 700px;
  max-height: 95vh;
  overflow-y: auto;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-100px) scale(0.8) rotateX(15deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  border-bottom: 2px solid rgba(0, 70, 67, 0.1);
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
  border-radius: 25px 25px 0 0;
  position: relative;
  overflow: hidden;
}

.modal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

.modal-header h3 {
  margin: 0;
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(220, 53, 69, 0.9);
  border-color: #dc3545;
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

/* Form Styles - تحسين تصميم النموذج */
.add-product-form {
  padding: 2.5rem;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 2rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.form-group label::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
  border-radius: 2px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 4px rgba(0, 70, 67, 0.1), 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

/* Image Upload Styles - تحسين تصميم رفع الصور */
.image-upload {
  text-align: center;
  padding: 2rem;
  padding-bottom: 120px;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  border-radius: 20px;
  border: 2px dashed #dee2e6;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.image-upload::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(0, 70, 67, 0.05) 0%,
    transparent 70%
  );
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.image-upload:hover {
  border-color: var(--primary-color);
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  transform: scale(1.02);
}

.image-upload input[type="file"] {
  display: none;
}

.image-upload-label {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 3rem;
  background: linear-gradient(135deg, #6f42c1, #8b5cf6);
  color: white;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
}

.image-upload-label:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 35px rgba(111, 66, 193, 0.4);
}

.image-upload-label svg {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.image-preview {
  margin-top: 1.5rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.image-preview img {
  display: inline-block;
  max-width: 250px;
  max-height: 250px;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  object-fit: cover;
  border: 3px solid white;
  transition: all 0.3s ease;
}

.image-preview img:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* Form Actions - تحسين تصميم الأزرار */
.form-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid rgba(0, 70, 67, 0.1);
}

.submit-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 1.2rem 3rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
}

.submit-btn svg {
  font-size: 1.3rem;
  animation: pulse 2s infinite;
}

.cancel-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
  border: none;
  padding: 1.2rem 3rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.cancel-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 35px rgba(108, 117, 125, 0.4);
}

/* Products Display - تحسين عرض المنتجات */
.products-display {
  margin-top: 3rem;
}

.product-category {
  margin-bottom: 4rem;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 25px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(0, 70, 67, 0.1);
  position: relative;
  overflow: hidden;
}

.product-category::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
}

.category-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 3rem;
  padding-bottom: 1.5rem;
  border-bottom: 3px solid rgba(0, 70, 67, 0.1);
  color: var(--primary-color);
  font-size: 2.2rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.category-title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 1rem;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-order-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.order-btn {
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.2);
}

.order-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d5a5a, var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 70, 67, 0.3);
}

.order-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
  transform: none;
  box-shadow: none;
}

.category-title::after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-color), #2d5a5a);
  border-radius: 3px;
}

.items-count {
  background: linear-gradient(135deg, #004643, #004643);
  color: white;
  padding: 0.6rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.3);
  animation: pulse 2s infinite;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.product-card {
  background: var(--white);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid var(--border-color);
  position: relative;
  box-shadow: var(--shadow-light);
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(44, 85, 48, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.product-card:hover::before {
  opacity: 1;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-heavy);
  border-color: var(--accent-color);
}

.product-image {
  height: 220px;
  overflow: hidden;
  position: relative;
  border-radius: 25px 25px 0 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.1) rotate(2deg);
}

.product-details {
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.product-name {
  font-size: 1.4rem;
  text-align: center;
  font-weight: 700;
  color: var(--headline-color);
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

.product-desc {
  color: var(--paragraph-color);
  font-size: 0.95rem;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.product-price {
  font-size: 1.8rem;
  text-align: center;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
}

.product-actions {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  display: flex;
  gap: 0.8rem;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
}

.product-card:hover .product-actions {
  opacity: 1;
  transform: translateY(-5px);
}

.delete-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: scale(1.2) rotate(10deg);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
}

.delete-btn svg {
  font-size: 1.2rem;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

/* Empty State - تحسين حالة عدم وجود منتجات */
.empty-state {
  text-align: center;
  padding: 5rem 3rem;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 30px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 2px dashed rgba(0, 70, 67, 0.2);
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(0, 70, 67, 0.03) 0%,
    transparent 70%
  );
  animation: rotate 30s linear infinite;
}

.empty-icon {
  font-size: 5rem;
  margin-bottom: 2rem;
  color: var(--primary-color);
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.empty-state h3 {
  color: var(--primary-color);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.empty-state p {
  color: var(--paragraph-color);
  font-size: 1.3rem;
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .admin-header h1 {
    font-size: 2rem;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .category-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.barista-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.barista-filters button {
  background-color: var(--white);
  border: 2px solid #004643;
  color: var(--text-color);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.barista-filters button.active {
  background-color: #004643;
  color: var(--white);
  border-color: #004643;
}

.barista-filters button:hover {
  border-color: var(--primary-color);
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  width: 100%;
  padding: 0;
}

.order-card {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border-left: 5px solid #ddd;
  cursor: pointer;
  transition: var(--transition);
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left-color: #ff9800;
}

.order-card.confirmed {
  border-left-color: #2196f3;
}

.order-card.preparing {
  border-left-color: #9c27b0;
}

.order-card.ready {
  border-left-color: #4caf50;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-info h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.order-time {
  color: var(--light-text);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.order-status {
  color: var(--white);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.order-items {
  margin-bottom: 1rem;
}

.order-item {
  padding: 0.3rem 0;
  color: var(--text-color);
  font-size: 0.9rem;
}

.more-items {
  color: var(--light-text);
  font-style: italic;
  font-size: 0.8rem;
}

.order-total {
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.order-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.order-actions button {
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: var(--white);
}

.confirm-btn:hover {
  background-color: var(--headline-color);
}

.prepare-btn {
  background-color: #9c27b0;
  color: var(--white);
}

.prepare-btn:hover {
  background-color: #7b1fa2;
}

.ready-btn {
  background-color: #4caf50;
  color: var(--white);
}

.ready-btn:hover {
  background-color: #388e3c;
}

.view-btn {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.view-btn:hover {
  background-color: #e0d5cc;
}

.no-orders {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: var(--light-text);
}

.no-orders svg {
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

/* Order Modal Details */
.barista-order-content .order-details {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.barista-order-content .order-details p {
  margin: 0.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  font-size: 1rem;
  color: #333;
}

.barista-order-content .order-details strong {
  color: var(--primary-color);
  font-weight: 600;
  min-width: 120px;
}

.barista-order-content .order-items-detail {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.barista-order-content .order-items-detail h3 {
  color: var(--primary-color);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.order-items-detail h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.barista-order-content .item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.barista-order-content .item-detail:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.barista-order-content .item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.barista-order-content .item-name {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1.1rem;
  display: block;
}

.barista-order-content .item-desc {
  font-size: 0.9rem;
  color: #666;
  opacity: 1;
}

.barista-order-content .item-qty-price {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-direction: column;
  align-items: flex-end;
}

.barista-order-content .qty {
  background: #004643;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 700;
  min-width: 35px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 70, 67, 0.2);
}

.barista-order-content .price {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.barista-order-content .order-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.barista-order-content .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  font-size: 1rem;
  color: #333;
}

.barista-order-content .summary-row.total {
  background: linear-gradient(135deg, #004643, #004643);
  color: rgb(0, 0, 0);
  font-weight: 700;
  font-size: 1.2rem;
  border: 2px solid var(--primary-color);
  margin-top: 1rem;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.3);
}

/* Accountant Dashboard Styles */
.accountant-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--gradient-background);
  min-height: 100vh;
}

.accountant-header {
  background: #004643;
  color: var(--text-light);
  padding: 2rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-medium);
  text-align: center;
}

.accountant-header h1 {
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 700;
}

.period-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.period-selector select {
  padding: 0.75rem 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 25px;
  font-size: 1rem;
  background: var(--white);
  color: var(--text-dark);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.period-selector select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.2);
}

.export-btn {
  background: #f9bc60;
  color: #004643;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-light);
}

.export-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(145deg, var(--white), var(--light-gray));
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.stat-icon {
  background: var(--gradient-primary);
  color: var(--text-light);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: var(--shadow-light);
}

.stat-content h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.stat-content p {
  margin: 0.5rem 0 0 0;
  color: var(--paragraph-color);
  font-size: 0.9rem;
  opacity: 0.8;
}

.accountant-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.ready-orders-section {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.ready-orders-section h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.ready-orders-grid {
  display: grid;
  gap: 1rem;
}

.ready-order-card {
  background-color: var(--secondary-color);
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #4caf50;
}

.ready-order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ready-order-card h3 {
  margin: 0;
  color: var(--primary-color);
}

.order-time {
  color: var(--light-text);
  font-size: 0.9rem;
}

.payment-btn {
  background-color: #28a745;
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  width: 100%;
  justify-content: center;
  margin-top: 1rem;
}

.payment-btn:hover {
  background-color: #218838;
}

.popular-items-section,
.sales-summary {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
}

.popular-items-section h2,
.sales-summary h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.popular-items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.popular-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--secondary-color);
  border-radius: 8px;
}

.rank {
  background-color: var(--primary-color);
  color: var(--white);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.item-name {
  flex: 1;
  font-weight: 500;
}

.item-count {
  color: var(--light-text);
  font-size: 0.9rem;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--secondary-color);
  border-radius: 8px;
}

.summary-item svg {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.summary-item div {
  display: flex;
  flex-direction: column;
}

.summary-item span:first-child {
  color: var(--light-text);
  font-size: 0.9rem;
}

.summary-item strong {
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* Payment Modal */
.payment-summary {
  background-color: var(--secondary-color);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.payment-method {
  margin-bottom: 1.5rem;
}

.payment-method h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.payment-options {
  display: flex;
  gap: 1rem;
}

.payment-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--secondary-color);
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
}

.payment-options label:hover {
  border-color: var(--primary-color);
}

.payment-options input[type="radio"] {
  margin: 0;
}

.payment-options input[type="radio"]:checked + svg {
  color: var(--primary-color);
}

/* QR Manager Styles */
.qr-manager-container {
  max-width: 1400px;
  margin: 0 auto;
}

.qr-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid var(--secondary-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.qr-manager-header h1 {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.table-number-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.number-input {
  display: flex;
  align-items: center;
  border: 1px solid var(--secondary-color);
  border-radius: 5px;
  overflow: hidden;
}

.number-input button {
  background-color: #004643;
  color: var(--white);
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.number-input button:hover:not(:disabled) {
  background-color: #016b31;
}

.number-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.number-input input {
  border: none;
  padding: 0.5rem;
  width: 80px;
  text-align: center;
  font-size: 1rem;
}

.bulk-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.add-table-btn,
.download-all-btn,
.print-all-btn {
  background: var(--gradient-primary);
  color: var(--text-light);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-weight: 600;
  box-shadow: var(--shadow-light);
}

.add-table-btn {
  background: linear-gradient(135deg, #004643, #006b5d);
}

.download-all-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
}

.print-all-btn {
  background: linear-gradient(135deg, #17a2b8, #138496);
}

.add-table-btn:hover {
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.download-all-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.print-all-btn:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.loading-container {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--secondary-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.qr-codes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.qr-code-card {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  text-align: center;
  transition: var(--transition);
}

.qr-code-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.qr-card-header h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.qr-code-display {
  margin-bottom: 1rem;
}

.qr-code-display img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
}

.qr-code-display img:hover {
  transform: scale(1.05);
}

.qr-card-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* QR Manager Buttons */
.qr-card-actions .download-btn,
.qr-card-actions .print-btn,
.qr-card-actions .delete-btn {
  background: var(--gradient-primary);
  color: var(--text-light);
  border: none;
  padding: 0rem;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: var(--shadow-light);
  flex: 1;
  justify-content: center;
}

.qr-card-actions .download-btn {
  background: linear-gradient(135deg, #004643, #006b5d);
}

.qr-card-actions .print-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
}

.qr-card-actions .delete-btn {
  background: linear-gradient(135deg, var(--danger-color), #c82333);
}

.qr-card-actions .download-btn:hover {
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.qr-card-actions .print-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.qr-card-actions .delete-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

/* QR Modal Actions */
.qr-modal .modal-actions .download-btn,
.qr-modal .modal-actions .print-btn {
  background: var(--gradient-accent);
  color: var(--button-text-color);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: var(--shadow-light);
  flex: 1;
  justify-content: center;
}

.qr-modal .modal-actions .print-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
}

.qr-modal .modal-actions .download-btn:hover {
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.qr-modal .modal-actions .print-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
}

.qr-preview {
  text-align: center;
  margin-bottom: 1.5rem;
}

.qr-preview img {
  max-width: 300px;
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.qr-info {
  background-color: var(--secondary-color);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.qr-info p {
  margin-bottom: 1rem;
}

.qr-info strong {
  color: var(--primary-color);
}

.qr-instructions {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  margin-top: 2rem;
}

.qr-instructions h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.qr-instructions ol {
  color: var(--text-color);
  line-height: 1.6;
}

.qr-instructions li {
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .accountant-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-nav {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .content-container {
    flex-direction: column;
  }

  .cart-container {
    position: static;
    width: 100%;
    margin-top: 20px;
  }

  .menu-items {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .orders-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .barista-container {
    padding: 15px;
  }

  .barista-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .barista-header h1 {
    font-size: 1.5rem;
  }

  .header-actions {
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
  }

  .action-buttons-group {
    justify-content: center;
  }

  .secondary-buttons-group {
    justify-content: center;
  }

  .primary-btn,
  .secondary-btn,
  .danger-btn {
    font-size: 12px !important;
    padding: 0.5rem 0.75rem !important;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
  }

  .qr-codes-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .barista-header,
  .accountant-header,
  .qr-manager-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .table-controls {
    justify-content: center;
  }

  .modal-content {
    padding: 1rem;
  }

  .checkout-modal,
  .order-modal,
  .payment-modal,
  .qr-modal {
    width: 95%;
    margin: 1rem;
  }

  /* Responsive Order Modal */
  .order-modal .modal-content {
    padding: 1.5rem;
    gap: 1rem;
  }

  .order-modal .order-details,
  .order-modal .order-items-detail,
  .order-modal .order-summary {
    padding: 1rem;
  }

  .order-modal .order-details p {
    padding: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .order-modal .order-details strong {
    min-width: auto;
  }

  .order-modal .item-detail {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .order-modal .item-qty-price {
    flex-direction: row;
    align-items: center;
    align-self: flex-end;
    gap: 1rem;
    min-width: auto;
  }

  .order-modal .modal-actions {
    padding: 1rem 1.5rem;
  }

  .order-modal .collect-payment-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .nav-links {
    flex-direction: column;
    width: 100%;
  }

  .nav-link {
    justify-content: center;
    width: 100%;
  }

  /* Mobile Order Modal */
  .order-modal-overlay {
    padding: 0.5rem;
  }

  .order-modal {
    width: 100%;
    max-height: 95vh;
    border-radius: 15px;
  }

  .order-modal .modal-header {
    padding: 1rem 1.5rem;
    border-radius: 15px 15px 0 0;
  }

  .order-modal .modal-header h2 {
    font-size: 1.2rem;
  }

  .order-modal .modal-content {
    padding: 1rem;
    gap: 1rem;
  }

  .order-modal .order-details,
  .order-modal .order-items-detail,
  .order-modal .order-summary {
    padding: 1rem;
  }

  .order-modal .order-details p {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .order-modal .item-detail {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .order-modal .item-name {
    font-size: 1rem;
  }

  .order-modal .item-desc {
    font-size: 0.85rem;
  }

  .order-modal .modal-actions {
    padding: 1rem;
  }

  .order-modal .collect-payment-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .barista-filters {
    flex-direction: column;
  }

  .barista-filters button {
    width: 100%;
  }

  .payment-options {
    flex-direction: column;
  }

  .bulk-actions {
    flex-direction: column;
    width: 100%;
  }

  .download-all-btn,
  .print-all-btn,
  .add-table-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Customer App Styles - Redesigned */
.customer-app {
  min-height: 100vh;
  background: var(--background-color);
}

/* Hero Section */
.customer-hero {
  background: #004643;
  color: var(--text-light);
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 0 0 30px 30px;
  box-shadow: var(--shadow-medium);
}

.hero-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.staff-login-link {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.staff-login-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.staff-login-link svg {
  font-size: 16px;
}

.customer-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.cafe-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 3rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem;
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.cafe-logo h1 {
  font-size: 3rem;
  margin: 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.1rem;
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
  font-weight: 300;
}

.table-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 1.1rem;
  font-weight: 600;
}

.table-selection-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.qr-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.qr-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.qr-section h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.qr-section p {
  margin: 0;
  opacity: 0.8;
  font-size: 1rem;
}

.divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.divider span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  position: relative;
  z-index: 1;
}

.manual-selection h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.table-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(10px);
}

.table-select option {
  background: var(--primary-color);
  color: white;
}

/* Main Content */
.main-content {
  width: 100%;
  padding: 0 2rem 2rem 2rem;
  position: relative;
}

.menu-section-wrapper {
  margin-right: 380px; /* Space for floating cart */
}

/* Category Navigation */
.category-navigation {
  margin-bottom: 3rem;
}

.category-navigation h2 {
  color: var(--text-color);
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 600;
}

.category-tabs-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.category-tabs {
  display: flex;
  gap: 0.5rem;
  background: white;
  padding: 0.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
  max-width: 100%;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-color);
  border-radius: 10px;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
  font-weight: 500;
  font-size: 0.95rem;
}

.category-tab:hover {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
}

.category-tab.active {
  background: linear-gradient(135deg, var(--primary-color), #004643);
  color: white;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-text {
  font-weight: inherit;
}

/* Menu Display */
.menu-display {
  margin-bottom: 2rem;
}

.menu-category-section {
  margin-bottom: 4rem;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.category-title {
  color: #004643;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  white-space: nowrap;
}

.category-line {
  flex: 1;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  border-radius: 2px;
}

.menu-items-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

/* Menu Item Card */
.menu-items .menu-item-card {
  background: var(--white);
  border-radius: 16px;
  overflow: hidden;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  display: flex;
  flex-direction: row;
  cursor: pointer;
  height: 120px;
  align-items: center;
}

.menu-items .menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-color);
}

.menu-items .menu-item-card.hovered {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.menu-items .menu-item-image {
  width: 120px;
  height: 120px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  order: 2;
}

.menu-items .menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.menu-items .item-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  order: 1;
  height: 100%;
}

.menu-items .item-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.menu-items .item-info h5 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  text-align: right;
  line-height: 1.3;
}

.menu-items .item-desc {
  font-size: 0.9rem;
  color: var(--paragraph-color);
  text-align: right;
  margin: 0;
  line-height: 1.4;
}

.menu-items .item-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: right;
  margin: 0;
}

.menu-items .add-to-cart-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  margin-top: 0.5rem;
  align-self: flex-start;
}

.menu-items .add-to-cart-btn:hover {
  background: var(--headline-color);
  transform: translateY(-1px);
}

.menu-items .menu-item-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-color);
}

.menu-items .menu-item-card:hover .menu-item-image img {
  transform: scale(1.05);
}

.menu-items .menu-item-card.adding {
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-color: var(--primary-color);
  transform: scale(1.02);
}

.adding-feedback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  z-index: 10;
  animation: fadeInOut 0.5s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.image-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0;
}

.price-badge {
  background: linear-gradient(135deg, var(--primary-color), #5a67d8);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
}

/* Item Content for vertical cards */
.item-image-container + .item-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-header {
  margin-bottom: 0.5rem;
}

.item-name {
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-description {
  color: var(--paragraph-color);
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 2.4em;
}

.item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  margin-top: auto;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.2rem;
}

.qty-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: white;
  color: var(--primary-color);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.qty-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.qty-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qty-btn.minus:disabled {
  background: #e9ecef;
  color: #6c757d;
}

.quantity-display {
  min-width: 30px;
  text-align: center;
  font-weight: 700;
  font-size: 0.9rem;
  color: var(--text-color);
}

/* Remove old button styles - no longer needed */

.add-to-cart-button {
  background: var(--gradient-accent);
  color: var(--button-text-color);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: var(--shadow-light);
  flex: 1;
  justify-content: center;
}

.add-to-cart-button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
}

.add-to-cart-button.adding {
  background: linear-gradient(135deg, #17a2b8, #138496);
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.add-to-cart-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Floating Cart */
.floating-cart {
  position: fixed;
  top: 2rem;
  right: 2rem;
  width: 350px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: var(--transition);
  border: 2px solid #f0f0f0;
}

.floating-cart.open {
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

.floating-cart.closed {
  max-height: 80px;
  overflow: hidden;
}

.cart-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20px 20px 0 0;
  transition: var(--transition);
}

.cart-header:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

.cart-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cart-icon {
  font-size: 1.3rem;
  color: var(--primary-color);
}

.cart-title h3 {
  margin: 0;
  color: #004643;
  font-size: 1.3rem;
  font-weight: 700;
}

.cart-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart-badge {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.item-count {
  background: linear-gradient(135deg, var(--primary-color), #5a67d8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.total-price {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

.toggle-icon {
  color: #004643;
  font-size: 1.2rem;
  transition: var(--transition);
}

.cart-header:hover .toggle-icon {
  color: var(--primary-color);
}

/* Cart Content */
.cart-content {
  padding: 1.5rem;
}

.empty-cart {
  text-align: center;
  padding: 2rem 1rem;
  color: #6c757d;
}

.empty-cart-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-cart h4 {
  margin: 0 0 0.5rem 0;
  color: #004643;
  font-size: 1.2rem;
}

.empty-cart p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Cart Items */
.cart-items {
  margin-bottom: 1.5rem;
}

.cart-item {
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-details {
  margin-bottom: 1rem;
}

.cart-item .item-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.cart-item .item-price {
  font-size: 0.9rem;
  color: var(--paragraph-color);
  margin: 0;
}

.cart-item .item-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.cart-item .quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  padding: 0.25rem;
}

.cart-item .qty-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: white;
  color: var(--primary-color);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cart-item .qty-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.cart-item .qty-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cart-item .quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: var(--text-color);
}

.item-total {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.remove-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Cart Totals */
.cart-totals {
  border-top: 2px solid #f0f0f0;
  padding-top: 1.5rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.total-row span:first-child {
  color: var(--paragraph-color);
}

.total-row span:last-child {
  font-weight: 600;
  color: var(--text-color);
}

.final-total {
  font-size: 1.1rem;
  font-weight: 700;
  padding: 0.75rem 0;
  border-top: 1px solid #f0f0f0;
  margin-bottom: 1.5rem;
}

.final-total span {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.checkout-button {
  width: 100%;
  background: var(--gradient-accent);
  color: var(--button-text-color);
  border: none;
  padding: 1.25rem 2rem;
  border-radius: 30px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-medium);
}

.checkout-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-heavy);
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
}

/* Responsive Design for Customer App */
@media (min-width: 1600px) {
  .menu-items-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .main-content {
    padding: 0 3rem 3rem 3rem;
  }

  .menu-section-wrapper {
    margin-right: 400px;
  }
}

@media (max-width: 1400px) {
  .menu-items-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1200px) {
  .menu-section-wrapper {
    margin-right: 0;
  }

  .floating-cart {
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
    margin-top: 2rem;
    border-radius: 15px;
  }

  .main-content {
    padding: 1.5rem;
  }

  .menu-items-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
}

@media (max-width: 900px) {
  .menu-items-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .customer-hero {
    padding: 1rem;
  }

  .hero-header {
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
  }

  .staff-login-link {
    padding: 8px 12px;
    font-size: 13px;
  }

  .cafe-logo {
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .logo-icon {
    font-size: 2.5rem;
    padding: 0.75rem;
  }

  .cafe-logo h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .table-selection-card {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .main-content {
    padding: 0 1rem 1rem 1rem;
  }

  .category-tabs {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .category-tab {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .menu-items-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .menu-items .menu-item-card {
    height: 110px;
  }

  .menu-items .menu-item-image {
    width: 90px;
  }

  .item-image-container {
    height: 160px;
  }

  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .category-title {
    font-size: 1.5rem;
  }

  .floating-cart {
    width: calc(100% - 2rem);
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0 0.5rem 0.5rem 0.5rem;
  }

  .customer-hero {
    padding: 0.75rem 0.5rem;
    border-radius: 0 0 10px 10px;
  }

  .cafe-logo h1 {
    font-size: 1.75rem;
  }

  .menu-items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .menu-items .menu-item-card {
    border-radius: 12px;
    height: 200px;
  }

  .menu-items .menu-item-image {
    width: 70px;
  }

  .item-image-container {
    height: 140px;
  }

  .item-content {
    padding: 0.75rem;
  }

  .item-name {
    font-size: 1rem;
  }

  .item-description {
    font-size: 0.8rem;
  }

  .floating-cart {
    width: calc(100% - 1rem);
    margin: 0.5rem;
    border-radius: 12px;
  }

  .category-tabs {
    padding: 0.25rem;
    gap: 0.25rem;
  }

  .category-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .tab-icon {
    font-size: 1rem;
  }

  .price-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 360px) {
  .menu-items-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Staff App Styles */
.staff-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.staff-nav {
  background-color: #2c3e50;
  color: var(--white);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow);
}

.staff-nav .nav-brand h2 {
  margin: 0;
  color: var(--white);
}

.staff-nav .nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.staff-nav .nav-link {
  color: var(--white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: var(--transition);
}

.staff-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
}

.customer-link {
  background-color: #28a745 !important;
}

.customer-link:hover {
  background-color: #218838 !important;
}

.staff-content {
  flex: 1;
  padding: 2rem;
}

/* Staff Dashboard */
.staff-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-card {
  background-color: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow);
  text-decoration: none;
  color: inherit;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.dashboard-card .card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
}

.barista-card .card-icon {
  background-color: #8b4513;
}

.waiter-card .card-icon {
  background-color: #007bff;
}

.available-orders-card .card-icon {
  background-color: #17a2b8;
}

.accountant-card .card-icon {
  background-color: #28a745;
}

.qr-card .card-icon {
  background-color: #004643;
}

.admin-card .card-icon {
  background-color: #dc3545;
}

.customer-card .card-icon {
  background-color: #17a2b8;
}

.stats-card .card-icon {
  background-color: #ffc107;
  color: var(--text-color);
}

.dashboard-card .card-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
}

.dashboard-card .card-content p {
  margin: 0;
  color: var(--light-text);
  font-size: 0.9rem;
}

.quick-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.info-section {
  background-color: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.info-section h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.info-section ul {
  list-style-type: none;
  padding: 0;
}

.info-section li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--secondary-color);
}

.info-section li:last-child {
  border-bottom: none;
}

.quick-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-links a {
  color: var(--primary-color);
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 5px;
  transition: var(--transition);
}

.quick-links a:hover {
  background-color: var(--secondary-color);
}

/* Twitter notification styles removed */

/* Add Table Form Styles */
.add-table-form {
  margin-bottom: 1.5rem;
}

.form-info {
  background-color: var(--secondary-color);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.form-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-color);
}

.confirm-btn {
  background-color: #004643;
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-weight: 600;
}

.confirm-btn:hover:not(:disabled) {
  background-color: var(--headline-color);
}

.confirm-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Waiter Page Styles */
.waiter-login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.waiter-login-modal {
  background-color: var(--white);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  min-width: 400px;
}

.login-header {
  margin-bottom: 2rem;
  color: var(--primary-color);
}

.login-header svg {
  color: #007bff;
  margin-bottom: 1rem;
}

.login-form .form-group {
  margin-bottom: 1.5rem;
  text-align: right;
}

.login-form label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 600;
}

.login-form input {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--secondary-color);
  border-radius: 8px;
  font-size: 1.1rem;
  text-align: center;
  transition: var(--transition);
}

.login-form input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.login-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  transition: var(--transition);
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.waiter-container {
  width: 100%;
  min-height: 100vh;
  background: var(--gradient-background);
  padding: 2rem;
  box-sizing: border-box;
}

.waiter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #004643;
  color: var(--text-light);
  padding: 2rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-medium);
  width: 100%;
  box-sizing: border-box;
}

.waiter-info h1 {
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.waiter-info p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.waiter-actions {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.sales-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1.5rem;
  margin-top: 1.8rem;
  border-radius: 25px;
  font-weight: 500;
}

.ready-orders-section {
  background-color: var(--white);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.ready-orders-section h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ready-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.ready-order-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid #28a745;
  border-radius: 12px;
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.ready-order-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #28a745, #20c997);
}

.ready-order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
}

.ready-order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-info h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.3rem;
}

.ready-time {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #28a745;
  font-weight: 500;
  font-size: 0.9rem;
}

.ready-order-card .order-summary {
  margin-bottom: 1.5rem;
}

.customer-info {
  margin-bottom: 1rem;
}

.customer-info p {
  margin: 0.3rem 0;
  color: var(--text-color);
}

.ready-order-card .order-total {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
}

.ready-order-card .order-actions {
  display: flex;
  gap: 1rem;
}

.view-details-btn {
  background-color: #6c757d;
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  flex: 1;
  justify-content: center;
}

.view-details-btn:hover {
  background-color: #5a6268;
}

.collect-payment-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  flex: 2;
  justify-content: center;
  font-weight: 600;
}

.collect-payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.waiter-stats {
  background: var(--white);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  margin-top: 1.5rem;
  border: 2px solid var(--border-color);
}

.waiter-stats h3 {
  color: var(--primary-color);
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, var(--white), var(--light-gray));
  padding: 1rem 1.25rem;
  border-radius: 12px;
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: #004643;
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

.stat-item svg {
  color: #004643;
  font-size: 1.8rem;
  flex-shrink: 0;
}

.stat-item div {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.stat-item span {
  color: var(--paragraph-color);
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
  opacity: 0.8;
}

.stat-item strong {
  color: var(--primary-color);
  font-size: 1.3rem;
  font-weight: 700;
  line-height: 1.2;
}

/* Payment Modal Styles */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Waiter Payment Modal - Higher z-index to ensure it appears above other modals */
.waiter-payment-modal {
  z-index: 1100 !important;
  background-color: rgba(0, 0, 0, 0.8);
}

/* Barista Order Modal - Standard z-index */
.barista-order-modal {
  z-index: 1000;
}

/* Ensure modals don't interfere with each other */
/* Cancel Order Button */
.barista-order-content .cancel-order-btn {
  background: #dc3545 !important;
  color: white !important;
  border: 2px solid #dc3545;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  min-width: 140px;
  justify-content: center;
  height: 45px;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.barista-order-content .cancel-order-btn:hover {
  background: #c82333 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.order-actions .cancel-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: var(--transition);
}

.order-actions .cancel-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
}

.barista-order-content {
  position: relative;
  z-index: 1001;
  background: white;
  border-radius: 20px;
  max-width: 700px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  margin: 1rem;
  display: flex;
  flex-direction: column;
}

.barista-order-content .modal-header {
  background: linear-gradient(135deg, #004643, #004643);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.barista-order-content .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.barista-order-content .close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.barista-order-content .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.barista-order-content .modal-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.barista-order-content .modal-actions {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 20px 20px;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  min-height: 80px;
  align-items: center;
}

.barista-order-content .modal-actions button {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  min-width: 140px;
  justify-content: center;
  height: 45px;
  color: white !important;
  text-align: center;
  line-height: 1.2;
  font-family: inherit;
  white-space: nowrap;
  overflow: visible;
}

.barista-order-content .confirm-btn {
  background: #28a745 !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  border: 2px solid #28a745;
}

.barista-order-content .confirm-btn:hover {
  background: #218838 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.barista-order-content .prepare-btn {
  background: #007bff !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  border: 2px solid #007bff;
}

.barista-order-content .prepare-btn:hover {
  background: #0056b3 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.barista-order-content .ready-btn {
  background: #ffc107 !important;
  color: #212529 !important;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
  border: 2px solid #ffc107;
  font-weight: 700;
}

.barista-order-content .ready-btn:hover {
  background: #e0a800 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

/* Responsive Design for Barista Order Modal */
@media (max-width: 768px) {
  .barista-order-content {
    width: 98%;
    max-height: 95vh;
    margin: 0.5rem;
    border-radius: 15px;
  }

  .barista-order-content .modal-header {
    padding: 1rem 1.5rem;
    border-radius: 15px 15px 0 0;
  }

  .barista-order-content .modal-header h2 {
    font-size: 1.2rem;
  }

  .barista-order-content .modal-content {
    padding: 1.5rem;
    gap: 1rem;
  }

  .barista-order-content .order-details,
  .barista-order-content .order-items-detail,
  .barista-order-content .order-summary {
    padding: 1rem;
  }

  .barista-order-content .item-detail {
    padding: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .barista-order-content .item-qty-price {
    flex-direction: row;
    align-items: center;
    align-self: flex-end;
    gap: 0.75rem;
  }

  .barista-order-content .modal-actions {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .barista-order-content .modal-actions button {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .barista-order-content {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
  }

  .barista-order-content .modal-header {
    border-radius: 0;
  }

  .barista-order-content .modal-content {
    padding: 1rem;
    flex: 1;
    overflow-y: auto;
  }

  .barista-order-content .modal-actions {
    border-radius: 0;
  }
}

.waiter-payment-content {
  position: relative;
  z-index: 1101;
}

.payment-modal {
  background-color: var(--white);
  border-radius: 15px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.payment-modal .modal-header {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  padding: 1.5rem 2rem;
  border-radius: 15px 15px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-modal .modal-header h2 {
  margin: 0;
  font-size: 1.3rem;
}

.payment-modal .close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.payment-modal .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.payment-modal .modal-content {
  padding: 2rem;
}

.payment-summary {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #28a745;
}

.payment-summary h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.payment-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.payment-summary .summary-row:last-child {
  border-bottom: none;
}

.payment-summary .summary-row.total {
  font-weight: bold;
  font-size: 1.1rem;
  color: #28a745;
  border-top: 2px solid #28a745;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.payment-method {
  margin-bottom: 2rem;
}

.payment-method h3 {
  margin: 0 0 1.5rem 0;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.payment-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.payment-options label {
  display: block;
  cursor: pointer;
  border: 2px solid #dee2e6;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition);
  background-color: var(--white);
}

.payment-options label:hover {
  border-color: #28a745;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.1);
}

.payment-options label.selected {
  border-color: #28a745;
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
}

.payment-options input[type="radio"] {
  display: none;
}

.payment-option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.payment-option-content svg {
  font-size: 2rem;
  color: #6c757d;
  transition: var(--transition);
}

.payment-options label.selected .payment-option-content svg {
  color: #28a745;
}

.payment-option-content span {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1.1rem;
}

.payment-options label.selected .payment-option-content span {
  color: #28a745;
}

.payment-modal .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.confirm-payment-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-weight: 600;
  font-size: 1.1rem;
}

.confirm-payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.cancel-btn {
  background-color: #6c757d;
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
}

.cancel-btn:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Payment breakdown styles */
.payment-breakdown {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e9ecef;
}

.payment-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.payment-stat svg {
  font-size: 0.8rem;
}

/* Enhanced statistics grid */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
  background: linear-gradient(135deg, #004643, #004643);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.stat-card .stat-content h3 {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.stat-card .stat-content p {
  color: #6c757d;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

/* Enhanced waiter cards */
.waiter-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.waiter-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.waiter-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

.waiter-rank {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.waiter-rank .trophy {
  color: #ffd700;
  font-size: 1.2rem;
}

.rank-number {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
}

.waiter-info h3 {
  color: var(--primary-color);
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.waiter-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.waiter-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-weight: 500;
}

.waiter-stats .stat svg {
  color: var(--primary-color);
  width: 16px;
}

.waiter-percentage {
  text-align: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.waiter-percentage span {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  display: block;
}

.waiter-percentage small {
  color: #6c757d;
  font-size: 0.8rem;
}

/* Waiter Selection Styles */
.waiter-selection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.waiter-selection-modal {
  background: white;
  border-radius: 20px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.waiter-selection-modal .modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 1.5rem;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.waiter-selection-modal .modal-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
}

.order-info {
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.order-info h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
}

.order-summary {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.order-summary span {
  background: white;
  padding: 0.5rem 1rem;
  margin: 2px;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  font-weight: 500;
}

.waiters-grid {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.waiter-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.waiter-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.waiter-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, #fff, #f8f9ff);
  box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
}

.waiter-avatar {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
}

.avatar-emoji {
  font-size: 3rem;
  display: block;
  margin-bottom: 0.5rem;
}

.availability-indicator {
  position: absolute;
  top: 0;
  right: 50%;
  transform: translateX(50%);
  background: #28a745;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  border: 2px solid white;
}

.waiter-info h3 {
  text-align: center;
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: 1.3rem;
}

.rating {
  text-align: center;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
  margin-bottom: 0.5rem;
}

.star {
  font-size: 1rem;
}

.star.filled {
  color: #ffc107;
}

.star.half {
  color: #ffc107;
}

.star.empty {
  color: #e9ecef;
}

.rating-text {
  font-size: 0.9rem;
  color: #6c757d;
}

.waiter-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
}

.detail-item svg {
  color: var(--primary-color);
  width: 16px;
}

.specialties,
.languages {
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.specialties strong,
.languages strong {
  color: var(--primary-color);
  display: block;
  margin-bottom: 0.5rem;
}

.specialty-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.specialty-tag {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.no-waiters {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.no-waiters svg {
  color: #dee2e6;
  margin-bottom: 1rem;
}

.modal-actions {
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.confirm-waiter-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-size: 1rem;
}

.confirm-waiter-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 144, 226, 0.4);
}

.confirm-waiter-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.selection-summary {
  padding: 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.summary-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid var(--primary-color);
}

.summary-card h4 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
}

.summary-card p {
  margin: 0.5rem 0;
  color: #495057;
}

/* Available Orders Page Styles */
.available-orders-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 2rem;
}

.page-header {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-content h1 {
  color: var(--primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 2rem;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #f9bc60, #f9bc60);
  color: #004643;
  padding: 1rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
}

.stats-bar {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: #6c757d;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.order-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
  transition: var(--transition);
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.order-number {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-number > span:first-child {
  font-weight: bold;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.time-elapsed {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.order-content {
  margin-bottom: 1.5rem;
}

.customer-info {
  margin-bottom: 1rem;
}

.customer-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #495057;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.total-amount {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1.1rem;
}

.items-preview {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  flex: 1;
  margin-right: 1rem;
}

.item-tag {
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  color: #495057;
  border: 1px solid #dee2e6;
}

.more-items {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-notes {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #856404;
}

.order-actions {
  display: flex;
  gap: 1rem;
}

.view-btn,
.claim-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.view-btn {
  background: #6c757d;
  color: white;
}

.view-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.claim-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.claim-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
}

.no-orders {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 15px;
  color: #6c757d;
}

.no-orders svg {
  color: #dee2e6;
  margin-bottom: 1rem;
}

.no-orders h3 {
  margin: 1rem 0;
  color: #495057;
}

/* Waiter Login Styles */
.waiter-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  padding: 2rem;
}

.waiter-login-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.login-header svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.login-header h2 {
  color: var(--primary-color);
  margin: 1rem 0;
}

.login-header p {
  color: #6c757d;
  margin-bottom: 2rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.login-form input {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  text-align: center;
  transition: var(--transition);
}

.login-form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.login-form button {
  background: #004643;
  color: var(--button-text-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow-light);
}

.login-form button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
  background: linear-gradient(135deg, #004643, #004643);
}

.login-form button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Order Details Modal */
.order-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.order-details-modal {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.order-details-modal .modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 1.5rem;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-details-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.order-details-modal .close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.order-details-modal .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.order-details-modal .modal-content {
  padding: 2rem;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item strong {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.info-item span {
  color: #495057;
  font-weight: 500;
}

.items-list {
  margin-bottom: 2rem;
}

.items-list h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
}

.item-qty {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: center;
}

.item-name {
  flex: 1;
  margin: 0 1rem;
  font-weight: 500;
  color: #495057;
}

.item-price {
  font-weight: bold;
  color: #001e1d;
}

.order-totals {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.final {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary-color);
  border-top: 2px solid var(--primary-color);
  padding-top: 1rem;
  margin-top: 0.5rem;
}

.notes-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.notes-section h3 {
  color: #856404;
  margin: 0 0 0.5rem 0;
}

.notes-section p {
  color: #856404;
  margin: 0;
  font-style: italic;
}

.order-details-modal .modal-actions {
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Waiter Tabs Styles */
.waiter-tabs {
  display: flex;
  background: var(--white);
  border-radius: 20px;
  padding: 0.75rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-light);
  gap: 0.75rem;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  background: transparent;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
  color: var(--paragraph-color);
}

.tab-btn:hover {
  background: var(--light-gray);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.tab-btn.active {
  background: #004643;
  color: var(--text-light);
  box-shadow: var(--shadow-light);
  transform: translateY(-2px);
}

/* New Orders Section - تصميم الطلبات الجديدة */
.new-orders-section {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem;
  border: 2px solid var(--border-color);
}

.new-orders-section h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.6rem;
  font-weight: 700;
}

.new-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
}

.new-order-card {
  background: linear-gradient(145deg, var(--white), var(--light-gray));
  border: 2px solid var(--accent-color);
  border-radius: 16px;
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.new-order-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.new-order-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.new-order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.new-order-card .table-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #004643;
  color: var(--text-light);
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  font-weight: 600;
  box-shadow: var(--shadow-light);
}

.new-order-card .order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--danger-color);
  background: #fff5f5;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 2px solid #f5c6cb;
  font-weight: 500;
}

.new-order-card .order-summary {
  margin-bottom: 1.5rem;
}

.new-order-card .customer-info p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--paragraph-color);
  font-weight: 500;
}

.new-order-card .items-preview {
  margin-top: 1rem;
}

.new-order-card .items-preview p {
  margin: 0 0 0.75rem 0;
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

.new-order-card .item-tag {
  background: linear-gradient(135deg, var(--secondary-color), #b8c9a8);
  color: var(--primary-color);
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  margin: 0.3rem 0.4rem 0.3rem 0;
  display: inline-block;
  border: 2px solid var(--accent-color);
  font-weight: 600;
}

.new-order-card .more-items {
  background: var(--gradient-accent);
  color: var(--text-light);
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
  box-shadow: var(--shadow-light);
}

.new-order-card .order-total {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--gradient-accent);
  color: #003530;
  padding: 1rem 1.25rem;
  border-radius: 15px;
  font-weight: 700;
  font-size: 1.2rem;
  margin-top: 1rem;
  box-shadow: var(--shadow-light);
}

.new-order-card .order-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.new-order-card .view-details-btn,
.new-order-card .accept-order-btn,
.new-order-card .reject-order-btn {
  flex: 1;
  min-width: 120px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-size: 0.9rem;
}

.new-order-card .view-details-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: var(--text-light);
  box-shadow: var(--shadow-light);
}

.new-order-card .view-details-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.new-order-card .accept-order-btn {
  background: var(--gradient-accent);
  color: var(--text-light);
  box-shadow: var(--shadow-light);
}

.new-order-card .accept-order-btn:hover {
  background: linear-gradient(135deg, var(--button-hover), #b8875a);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.new-order-card .reject-order-btn {
  background: linear-gradient(135deg, var(--danger-color), #c82333);
  color: var(--text-light);
  box-shadow: var(--shadow-light);
}

.new-order-card .reject-order-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Waiting Button for Orders in Progress */
.new-order-card .waiting-btn,
.ready-order-card .waiting-btn {
  flex: 1;
  min-width: 120px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 25px;
  cursor: not-allowed;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: var(--text-light);
  opacity: 0.7;
  box-shadow: var(--shadow-light);
}

/* No Orders State for New Orders */
.new-orders-section .no-orders {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--paragraph-color);
}

.new-orders-section .no-orders svg {
  color: #004643;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.new-orders-section .no-orders p {
  font-size: 1.2rem;
  margin: 0;
  font-weight: 500;
}

/* Ready Orders Section */
.ready-orders-section {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem;
  border: 2px solid var(--border-color);
}

.ready-orders-section h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.6rem;
  font-weight: 700;
}

.ready-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.ready-order-card {
  background: linear-gradient(145deg, var(--white), var(--light-gray));
  border: 2px solid #004643;
  border-radius: 16px;
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.ready-order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.ready-order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.ready-order-card .table-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.ready-order-card .ready-time {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  color: #dc3545;
  background: #fff5f5;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  border: 1px solid #f5c6cb;
}

.ready-order-card .order-summary {
  margin-bottom: 1.5rem;
}

.ready-order-card .customer-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #495057;
}

.ready-order-card .order-total {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: #003530;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1.1rem;
  margin-top: 1rem;
}

/* تصميم الأصناف في بطاقات الطلبات الجاهزة */
.ready-order-card .items-preview {
  margin-top: 0.5rem;
}

.ready-order-card .items-preview p {
  margin: 0 0 0.5rem 0;
  font-weight: bold;
  color: var(--primary-color);
}

.ready-order-card .item-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  margin: 0.2rem 0.3rem 0.2rem 0;
  display: inline-block;
  border: 1px solid #bbdefb;
}

.ready-order-card .more-items {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
}

/* زر إضافة طلب جديد */
.add-order-btn {
  background: linear-gradient(135deg, #ffffff, #ffffff);
  color: #002d2a;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.add-order-btn:hover {
  background: linear-gradient(135deg, #ff8906, #ff8906);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* نموذج إضافة طلب جديد */
.new-order-modal {
  background: white;
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.new-order-modal .modal-content {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.order-basic-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 70, 67, 0.1);
}

/* شبكة المنتجات */
.products-selection {
  margin-bottom: 2rem;
}

.products-selection h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  max-height: 500px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.product-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.product-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.product-info h4 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1rem;
}

.product-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  text-align: center;
}

.product-price {
  font-weight: bold;
  color: #28a745;
  font-size: 1rem;
}

.add-product-btn {
  background: #004643;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.add-product-btn:hover {
  background: #003d3a;
}

/* المنتجات المختارة */
.selected-items {
  margin-bottom: 2rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.selected-items h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.selected-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.item-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.item-name {
  font-weight: bold;
  color: var(--primary-color);
}

.item-price {
  color: #001e1d;
  font-size: 0.9rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 1rem;
}

.qty-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn:hover {
  background: #003d3a;
}

.quantity {
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.remove-item-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.remove-item-btn:hover {
  background: #c82333;
}

/* ملخص الطلب الجديد */
.order-summary-new {
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.order-summary-new h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.totals .total-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.totals .total-row.final {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary-color);
  border-bottom: none;
  border-top: 2px solid var(--primary-color);
  margin-top: 0.5rem;
  padding-top: 1rem;
}

/* أزرار النموذج */
.submit-order-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.submit-order-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.submit-order-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .order-basic-info {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .selected-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .quantity-controls {
    justify-content: center;
    margin: 0;
  }
}

.ready-order-card .order-actions {
  display: flex;
  gap: 1rem;
}

.ready-order-card .view-details-btn,
.ready-order-card .collect-payment-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.ready-order-card .view-details-btn {
  background: #6c757d;
  color: white;
}

.ready-order-card .view-details-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.ready-order-card .collect-payment-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.ready-order-card .collect-payment-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* زر إلغاء الطلب */
.cancel-order-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  flex: 1;
}

.cancel-order-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.cancel-order-btn:active {
  transform: translateY(0);
}

/* تحديث تخطيط أزرار الطلبات الجاهزة */
.ready-order-card .order-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.ready-order-card .view-details-btn,
.ready-order-card .collect-payment-btn,
.ready-order-card .cancel-order-btn {
  flex: 1;
  min-width: 100px;
}

/* No Orders State */
.no-orders {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  border: 2px dashed #dee2e6;
}

.no-orders svg {
  color: #adb5bd;
  margin-bottom: 1rem;
}

.no-orders h3 {
  color: var(--primary-color);
  margin: 1rem 0 0.5rem 0;
  font-size: 1.3rem;
}

.no-orders p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.no-orders .refresh-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.no-orders .refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.3);
}

/* Available Orders Section */
.available-orders-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-header h2 {
  color: var(--primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.refresh-btn,
.test-data-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  font-size: 0.9rem;
}

.refresh-btn:hover,
.test-data-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.3);
}

.test-data-btn {
  background: linear-gradient(135deg, #6c757d, #495057);
}

.test-data-btn:hover {
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.logout-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.stats-summary {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.stats-summary .stat-item {
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  font-size: 0.9rem;
  color: #495057;
}

.available-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.available-order-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.available-order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.available-order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.available-order-card .order-number {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.available-order-card .order-number > span:first-child {
  font-weight: bold;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.available-order-card .time-elapsed {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.available-order-card .table-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.available-order-card .order-content {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.available-order-card .customer-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.available-order-card .customer-info p {
  margin: 0.5rem 0;
  font-size: 0.95rem;
  color: #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.available-order-card .customer-info strong {
  color: var(--primary-color);
  font-weight: 600;
  min-width: 80px;
}

.available-order-card .order-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.available-order-card .total-amount {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: #003530;
  padding: 1rem;
  border-radius: 12px;
  font-weight: bold;
  font-size: 1.2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  margin-bottom: 1rem;
}

.available-order-card .items-preview {
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.available-order-card .items-preview h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 600;
}

.available-order-card .items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.available-order-card .item-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #bbdefb;
  display: inline-block;
  margin: 0.2rem 0.3rem 0.2rem 0;
}

.available-order-card .more-items {
  background: var(--primary-color);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
  margin: 0.2rem 0.3rem 0.2rem 0;
}

.available-order-card .order-notes {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #856404;
}

.available-order-card .order-actions {
  display: flex;
  gap: 1rem;
}

.available-order-card .view-btn,
.available-order-card .claim-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.available-order-card .view-btn {
  background: #6c757d;
  color: white;
}

.available-order-card .view-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.available-order-card .claim-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.available-order-card .claim-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
}

/* Order Details Modal Styles */
.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item strong {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.info-item span {
  color: #495057;
  font-weight: 500;
}

.items-list {
  margin-bottom: 2rem;
}

.items-list h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
}

.item-qty {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: center;
}

.item-name {
  flex: 1;
  margin: 0 1rem;
  font-weight: 500;
  color: #495057;
}

.item-price {
  font-weight: bold;
  color: #001e1d;
}

.order-totals {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.final {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary-color);
  border-top: 2px solid var(--primary-color);
  padding-top: 1rem;
  margin-top: 0.5rem;
}

.notes-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.notes-section h3 {
  color: #856404;
  margin: 0 0 0.5rem 0;
}

.notes-section p {
  color: #856404;
  margin: 0;
  font-style: italic;
}

/* Responsive Design for Waiter Page */
@media (max-width: 768px) {
  .waiter-container {
    padding: 1rem;
  }

  .waiter-login-modal {
    min-width: 300px;
    margin: 1rem;
  }

  .waiter-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1.5rem;
  }

  .waiter-actions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .waiter-tabs {
    padding: 0.25rem;
    margin-bottom: 1.5rem;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .ready-orders-grid,
  .available-orders-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .ready-order-card,
  .available-order-card {
    padding: 1rem;
  }

  .ready-order-card .order-actions,
  .available-order-card .order-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .ready-order-card .order-header,
  .available-order-card .order-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .section-actions {
    justify-content: center;
  }

  .stats-summary {
    flex-direction: column;
    gap: 0.5rem;
  }

  .payment-modal {
    width: 95%;
    margin: 1rem;
  }

  .payment-modal .modal-content {
    padding: 1.5rem;
  }

  .payment-options {
    grid-template-columns: 1fr;
  }

  .payment-modal .modal-actions {
    flex-direction: column;
  }

  .no-orders {
    padding: 2rem 1rem;
  }
}

/* Waiter Sales Main Section - قسم منفصل وعريض */
.waiter-sales-main-section {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  margin: 2rem 0;
  border: 2px solid #004643;
  position: relative;
  overflow: hidden;
}

.waiter-sales-main-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--gradient-accent);
}

.waiter-sales-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.waiter-sales-header h2 {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
}

.waiter-summary-cards {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.waiter-summary-cards .summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #004643;
  color: var(--text-light);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.waiter-summary-cards .summary-card svg {
  font-size: 1.5rem;
  color: #ffffff;
}

.waiter-summary-cards .summary-card div span {
  display: block;
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.waiter-summary-cards .summary-card div strong {
  font-size: 1.3rem;
  font-weight: 700;
}

.waiters-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Sales Summary Bottom Section */
.sales-summary-bottom {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-light);
  margin-top: 2rem;
  border: 2px solid var(--border-color);
}

.sales-summary-bottom h2 {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  justify-content: center;
}

.summary-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.sales-summary-bottom .summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(145deg, var(--white), var(--light-gray));
  padding: 1.5rem;
  border-radius: 16px;
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.sales-summary-bottom .summary-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.sales-summary-bottom .summary-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.sales-summary-bottom .summary-item svg {
  color: #004643;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.sales-summary-bottom .summary-item div span {
  display: block;
  color: var(--paragraph-color);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.sales-summary-bottom .summary-item div strong {
  color: var(--primary-color);
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1.2;
}

.waiter-sales-section h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.6rem;
  font-weight: 700;
}

.waiter-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #006c67;
}

.summary-card svg {
  color: #007bff;
  font-size: 2rem;
}

.summary-card div {
  display: flex;
  flex-direction: column;
}

.summary-card span {
  color: var(--light-text);
  font-size: 0.9rem;
}

.summary-card strong {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 700;
}

.no-waiter-sales {
  text-align: center;
  padding: 3rem;
  color: var(--light-text);
}

.no-waiter-sales svg {
  color: #6c757d;
  margin-bottom: 1rem;
}

.waiters-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.waiter-sales-section .waiter-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(145deg, var(--white), var(--light-gray));
  border-radius: 16px;
  padding: 1.25rem 2rem;
  transition: var(--transition);
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  width: 100%;
  min-height: 80px;
}

.waiter-sales-section .waiter-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.waiter-sales-section .waiter-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.waiter-sales-section .waiter-card:first-child::before {
  background: linear-gradient(135deg, #ffd700, #ffa500);
}

.waiter-sales-section .waiter-rank {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0;
  min-width: 60px;
  flex-shrink: 0;
}

.trophy {
  color: #ffc107;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.rank-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.waiter-sales-section .waiter-info {
  flex: 1;
  margin: 0 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.waiter-sales-section .waiter-info h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 700;
}

.waiter-sales-section .waiter-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.waiter-sales-section .waiter-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  color: var(--paragraph-color);
  font-size: 0.85rem;
  font-weight: 500;
}

.waiter-sales-section .waiter-stats .stat svg {
  color: var(--accent-color);
  font-size: 0.9rem;
}

.waiter-sales-section .waiter-percentage {
  text-align: center;
  margin: 0;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.waiter-sales-section .waiter-percentage span {
  display: block;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1.2;
}

.waiter-sales-section .waiter-percentage small {
  color: var(--paragraph-color);
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Responsive Design for Waiter Sales */
@media (max-width: 768px) {
  .waiter-summary {
    grid-template-columns: 1fr;
  }

  .waiter-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .waiter-rank,
  .waiter-info,
  .waiter-percentage {
    margin: 0;
  }

  .waiter-stats {
    justify-content: center;
    gap: 1rem;
  }
}

/* تحديث تخطيط أزرار الطلبات المتاحة */
.available-order-card .order-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.available-order-card .view-btn,
.available-order-card .claim-btn,
.available-order-card .cancel-order-btn {
  flex: 1;
  min-width: 100px;
}

/* تصميم متجاوب للطلبات الجديدة والجاهزة */
@media (max-width: 768px) {
  .new-orders-grid,
  .ready-orders-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .new-order-card,
  .ready-order-card,
  .available-order-card {
    padding: 1.25rem;
  }

  .new-order-card .order-actions,
  .ready-order-card .order-actions,
  .available-order-card .order-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .new-order-card .view-details-btn,
  .new-order-card .accept-order-btn,
  .new-order-card .reject-order-btn,
  .ready-order-card .view-details-btn,
  .ready-order-card .collect-payment-btn,
  .ready-order-card .cancel-order-btn,
  .available-order-card .view-btn,
  .available-order-card .claim-btn,
  .available-order-card .cancel-order-btn {
    width: 100%;
    min-width: auto;
  }

  .new-order-card .order-header,
  .ready-order-card .order-header,
  .available-order-card .order-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .new-orders-grid,
  .ready-orders-grid {
    gap: 0.75rem;
  }

  .new-order-card,
  .ready-order-card,
  .available-order-card {
    padding: 1rem;
  }

  .new-orders-section h2,
  .ready-orders-section h2 {
    font-size: 1.4rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  /* تصميم متجاوب للإحصائيات */
  .waiter-stats {
    padding: 1rem;
    margin-top: 1rem;
  }

  .waiter-stats h3 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .stat-item {
    padding: 0.75rem 1rem;
    gap: 0.5rem;
  }

  .stat-item svg {
    font-size: 1.5rem;
  }

  .stat-item strong {
    font-size: 1.1rem;
  }

  .stat-item span {
    font-size: 0.8rem;
  }
}

/* تصميم متجاوب للشاشات الصغيرة جداً */
@media (max-width: 360px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .stat-item svg {
    font-size: 1.3rem;
  }

  .stat-item strong {
    font-size: 1rem;
  }

  .stat-item span {
    font-size: 0.75rem;
  }

  .waiter-stats h3 {
    font-size: 1rem;
  }

  /* تصميم متجاوب لقسم مبيعات النادل الجديد */
  .waiter-sales-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .waiter-sales-header h2 {
    font-size: 1.4rem;
    text-align: center;
  }

  .waiter-summary-cards {
    justify-content: center;
    gap: 1rem;
  }

  .waiter-summary-cards .summary-card {
    min-width: auto;
    flex: 1;
    min-width: 180px;
  }

  .waiters-grid {
    gap: 0.75rem;
  }

  .waiter-sales-section .waiter-card {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
    min-height: auto;
  }

  .waiter-sales-section .waiter-info {
    margin: 0.5rem 0;
    text-align: center;
  }

  .waiter-sales-section .waiter-stats {
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .waiter-sales-section .waiter-rank,
  .waiter-sales-section .waiter-percentage {
    margin: 0;
  }

  /* تصميم متجاوب لملخص المبيعات */
  .summary-details-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .sales-summary-bottom .summary-item {
    padding: 1rem;
  }

  .sales-summary-bottom .summary-item div strong {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .waiter-summary-cards {
    flex-direction: column;
  }

  .waiter-summary-cards .summary-card {
    min-width: auto;
  }

  .summary-details-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .sales-summary-bottom {
    padding: 1.5rem;
  }

  .sales-summary-bottom h2 {
    font-size: 1.4rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* تصميم متجاوب لبطاقات النادل على الشاشات الصغيرة */
  .waiter-sales-section .waiter-card {
    padding: 0.75rem;
  }

  .waiter-sales-section .waiter-info h3 {
    font-size: 1.1rem;
  }

  .waiter-sales-section .waiter-stats .stat {
    font-size: 0.8rem;
  }

  .waiter-sales-section .waiter-percentage span {
    font-size: 1.1rem;
  }
}

/* تنسيق جدول مبيعات النادل البسيط */
.waiter-table-container {
  padding: 1.5rem;
  overflow-x: auto;
}

.waiter-sales-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.waiter-sales-table th {
  background: linear-gradient(135deg, #004643);
  color: white;
  padding: 1rem 0.75rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  border-bottom: 2px solid #002d2a;
}

.waiter-sales-table td {
  padding: 1rem 0.75rem;
  text-align: center;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
  font-size: 0.9rem;
}

.waiter-sales-table tbody tr {
  transition: all 0.3s ease;
}

.waiter-sales-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
}

.waiter-sales-table tbody tr:nth-child(even) {
  background-color: #fafbfc;
}

.waiter-sales-table .waiter-name {
  font-weight: 600;
  color: var(--primary-color);
}

.waiter-sales-table .sales-amount {
  font-weight: 700;
  color: #28a745;
}

.waiter-sales-table .percentage {
  font-weight: 600;
  color: #e16162;
}

.waiter-sales-table .trophy {
  color: #ffd700;
  margin-left: 0.5rem;
  font-size: 1.1rem;
}

/* تصميم متجاوب للجدول */
@media (max-width: 768px) {
  .waiter-table-container {
    padding: 1rem;
  }

  .waiter-sales-table {
    font-size: 0.8rem;
  }

  .waiter-sales-table th,
  .waiter-sales-table td {
    padding: 0.75rem 0.5rem;
  }

  .waiter-sales-table th {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .waiter-sales-table {
    font-size: 0.75rem;
  }

  .waiter-sales-table th,
  .waiter-sales-table td {
    padding: 0.5rem 0.25rem;
  }
}

/* تنسيق الاسم الإنجليزي في بطاقات المنتجات */
.product-name-en {
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
  margin: 0.25rem 0 0.5rem 0;
  font-family: "Arial", sans-serif;
}

/* تنسيق الهيدر المحسن لصفحة العملاء */
.customer-header {
  background: linear-gradient(135deg, var(--primary-color), #003a37);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.cafe-branding {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cafe-info {
  display: flex;
  flex-direction: column;
}

.cafe-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.cafe-tagline {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.cart-summary-header {
  display: flex;
  align-items: center;
}

.header-cart-info {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.cart-count {
  background: var(--accent-color);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 700;
  min-width: 1.5rem;
  text-align: center;
}

.cart-total {
  font-weight: 600;
  font-size: 0.9rem;
}

.staff-login-link {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.staff-login-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* تخطيط المحتوى المحسن */
.content-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  max-width: 100%;
  margin: 0 auto;
  padding: 2rem;
}

.menu-section {
  min-height: 100vh;
}

.cart-sidebar {
  position: sticky;
  top: 390px;
  height: fit-content;
}

/* تحسين السلة الجانبية */
.floating-cart {
  position: static !important;
  right: auto !important;
  bottom: auto !important;
  width: 100% !important;
  max-width: none !important;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid #004643;
}

.floating-cart.closed {
  height: auto !important;
}

/* تصميم متجاوب */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .cart-sidebar {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    width: 350px;
    z-index: 50;
  }

  .floating-cart {
    position: static !important;
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .cafe-branding {
    justify-content: center;
  }

  .cafe-name {
    font-size: 1.5rem;
  }

  .content-layout {
    padding: 1rem;
  }

  .cart-sidebar {
    width: calc(100% - 2rem);
    right: 1rem;
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 0.5rem;
  }

  .cafe-branding {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .header-cart-info {
    padding: 0.25rem 0.75rem;
    gap: 0.5rem;
  }

  .staff-login-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* تحسين الأزرار - عرض كامل وبدون أيقونات */
.checkout-button {
  width: 100% !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, var(--accent-color), #e6a84e) !important;
  color: var(--primary-color) !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  box-shadow: 0 4px 12px rgba(249, 188, 96, 0.3) !important;
}

.checkout-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(249, 188, 96, 0.4) !important;
  background: linear-gradient(135deg, #e6a84e, var(--accent-color)) !important;
}

.checkout-button:active {
  transform: translateY(0) !important;
}

.remove-btn {
  width: 100% !important;
  padding: 0.5rem 1rem !important;
  background: #dc3545 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.remove-btn:hover {
  background: #c82333 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

.qty-btn {
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px !important;
  border: 2px solid var(--accent-color) !important;
  background: white !important;
  color: var(--primary-color) !important;
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.qty-btn:hover:not(:disabled) {
  background: var(--accent-color) !important;
  color: var(--primary-color) !important;
  transform: scale(1.1) !important;
}

.qty-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.toggle-text {
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  color: var(--primary-color) !important;
  padding: 0.25rem 0.5rem !important;
  background: rgba(0, 70, 67, 0.1) !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.toggle-text:hover {
  background: rgba(0, 70, 67, 0.2) !important;
}

/* تحسين بطاقات المنتجات */
.menu-item-card {
  width: 100% !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 2px solid transparent !important;
}

.menu-item-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--accent-color) !important;
}

.menu-item-card.adding {
  transform: scale(0.98) !important;
  border-color: #28a745 !important;
  background: rgba(40, 167, 69, 0.05) !important;
}

.adding-feedback {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: #28a745 !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  z-index: 10 !important;
  animation: fadeInOut 0.5s ease !important;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* تحسين شريط التبويبات - عرض كامل وبدون أيقونات */
.category-tabs-container {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  background: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  border: 2px solid var(--accent-color) !important;
}

.category-tabs {
  display: flex !important;
  width: 100% !important;
  flex-wrap: wrap !important;
  gap: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.category-tab {
  flex: 1 !important;
  min-width: 0 !important;
  padding: 1rem 0.5rem !important;
  background: white !important;
  color: var(--primary-color) !important;
  border: none !important;
  border-right: 1px solid rgba(0, 70, 67, 0.1) !important;
  cursor: pointer !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  position: relative !important;
  justify-content: center !important; /* يوسّط العناصر أفقيًا */
  align-items: center !important; /* يوسّط العناصر عموديًا */
}

.category-tab:last-child {
  border-right: none !important;
}

.category-tab:hover {
  background: rgba(249, 188, 96, 0.1) !important;
  color: var(--primary-color) !important;
  transform: translateY(-2px) !important;
}

.category-tab.active {
  background: linear-gradient(135deg, var(--accent-color), #e6a84e) !important;
  color: var(--primary-color) !important;
  font-weight: 700 !important;
  box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.category-tab.active::before {
  content: "" !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: var(--primary-color) !important;
}

/* تصميم متجاوب للتبويبات */
@media (max-width: 768px) {
  .category-tab {
    font-size: 0.9rem !important;
    padding: 0.75rem 0.25rem !important;
  }
}

@media (max-width: 480px) {
  .category-tabs {
    flex-wrap: wrap !important;
  }

  .category-tab {
    flex: 1 1 calc(50% - 1px) !important;
    border-right: none !important;
    border-bottom: 1px solid rgba(0, 70, 67, 0.1) !important;
    font-size: 0.8rem !important;
    padding: 0.75rem 0.5rem !important;
  }

  .category-tab:nth-child(odd) {
    border-right: 1px solid rgba(0, 70, 67, 0.1) !important;
  }

  .category-tab:nth-last-child(-n + 2) {
    border-bottom: none !important;
  }
}

/* ===== تصميم صفحة تسجيل الدخول المحسن ===== */

/* الحاوية الرئيسية لصفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #abd1c6 0%, #004643 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: "Almarai", sans-serif;
}

/* إزالة جميع الخطوط والحدود من عناصر تسجيل الدخول */
.login-container *,
.login-card *,
.login-form *,
.login-container label,
.login-card label,
.login-form label,
.form-group label {
  text-decoration: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
  text-underline-offset: none !important;
  text-decoration-color: transparent !important;
  text-decoration-style: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* إزالة الخطوط من pseudo-elements في صفحة تسجيل الدخول */
.login-container *::before,
.login-container *::after,
.login-card *::before,
.login-card *::after,
.login-form *::before,
.login-form *::after,
.login-container label::before,
.login-container label::after,
.login-card label::before,
.login-card label::after,
.login-form label::before,
.login-form label::after,
.form-group label::before,
.form-group label::after {
  text-decoration: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
  text-underline-offset: none !important;
  text-decoration-color: transparent !important;
  text-decoration-style: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background-image: none !important;
  content: none !important;
  display: none !important;
}

/* بطاقة تسجيل الدخول */
.login-card {
  background: #fffffe;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 70, 67, 0.3);
  padding: 2.5rem;
  width: 100%;
  max-width: 450px;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #004643, #abd1c6, #004643);
}

/* هيدر تسجيل الدخول */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.cafe-logo {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.cafe-logo .avie-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  padding: 10px;
  background: linear-gradient(135deg, #004643, #abd1c6);
  box-shadow: 0 10px 30px rgba(0, 70, 67, 0.2);
  transition: all 0.3s ease;
}

.cafe-logo .avie-logo:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 70, 67, 0.3);
}

.login-header p {
  font-size: 1.5rem;
  font-weight: 700;
  color: #004643;
  margin: 0;
  letter-spacing: 0.5px;
}

/* نموذج تسجيل الدخول */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* رسالة الخطأ */
.error-message {
  background: linear-gradient(135deg, #e16162, #d63031);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(225, 97, 98, 0.3);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* مجموعة الحقول */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #004643;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  text-decoration: none !important;
  border: none !important;
  outline: none !important;
  border-bottom: none !important;
  text-decoration-line: none !important;
  text-underline-offset: none !important;
  text-decoration-color: transparent !important;
  text-decoration-style: none !important;
  box-shadow: none !important;
  background-image: none !important;
  background: transparent !important;
  position: relative;
}

/* إزالة أي pseudo-elements من labels */
.form-group label::before,
.form-group label::after {
  display: none !important;
  content: none !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
}

/* حاوية الإدخال */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  right: 1rem;
  color: #abd1c6;
  font-size: 1.1rem;
  z-index: 2;
  transition: all 0.3s ease;
}

.input-wrapper input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-family: "Almarai", sans-serif;
  background: #f8f9fa;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.1);
}

.input-wrapper input:focus {
  outline: none;
  background: #fffffe;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.2);
  transform: translateY(-1px);
}

.input-wrapper input:focus + .input-icon,
.input-wrapper input:not(:placeholder-shown) + .input-icon {
  color: #004643;
  transform: scale(1.1);
}

/* زر إظهار/إخفاء كلمة المرور */
.password-toggle {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #abd1c6;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  transition: all 0.3s ease;
  z-index: 3;
  outline: none;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #004643;
  transform: translateY(-50%) scale(1.1);
}

/* محدد نوع المستخدم */
.user-type-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.user-type-option {
  cursor: pointer;
  position: relative;
}

.user-type-option input[type="radio"] {
  display: none;
}

.user-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid #e8e4e6;
  border-radius: 12px;
  background: #fffffe;
  transition: all 0.3s ease;
  text-align: center;
}

.user-type-card svg {
  font-size: 2rem;
  color: #abd1c6;
  transition: all 0.3s ease;
}

.user-type-card span {
  font-weight: 600;
  color: #001e1d;
  font-size: 1rem;
}

.user-type-option input[type="radio"]:checked + .user-type-card {
  border-color: #004643;
  background: linear-gradient(135deg, #004643, #006b5d);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 70, 67, 0.3);
}

.user-type-option input[type="radio"]:checked + .user-type-card svg {
  color: #f9bc60;
  transform: scale(1.2);
}

.user-type-option input[type="radio"]:checked + .user-type-card span {
  color: white;
}

.user-type-card:hover {
  border-color: #abd1c6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(171, 209, 198, 0.2);
}

/* زر تسجيل الدخول */
.login-btn {
  background: linear-gradient(135deg, #004643, #006b5d);
  color: #fffffe;
  border: none;
  padding: 1.25rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  font-family: "Almarai", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  box-shadow: 0 8px 25px rgba(0, 70, 67, 0.3);
  width: 100%;
}

.login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #006b5d, #004643);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 70, 67, 0.4);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* تذييل النموذج */
.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e8e4e6;
}

.toggle-form-btn {
  background: linear-gradient(135deg, #abd1c6, #7fb3a3);
  border: 2px solid #004643;
  color: #004643;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(171, 209, 198, 0.3);
}

.toggle-form-btn:hover {
  background: linear-gradient(135deg, #004643, #006b5d);
  color: #fffffe;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 70, 67, 0.3);
}

/* حاوية رابط نسيان كلمة المرور */
.forgot-password-container {
  text-align: left;
  margin-top: 1rem;
  padding-top: 0.5rem;
}

/* رابط نسيان كلمة المرور */
.forgot-password-link {
  color: #e16162;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-family: "Almarai", sans-serif;
  padding: 0.25rem 0;
  border-bottom: 1px solid transparent;
}

.forgot-password-link:hover {
  color: #004643;
  border-bottom-color: #004643;
}

/* نافذة نسيان كلمة المرور */
.forgot-password-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.forgot-password-content {
  background: #fffffe;
  border-radius: 16px;
  padding: 2rem;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 70, 67, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.forgot-password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e8e4e6;
}

.forgot-password-header h3 {
  color: #004643;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.close-modal-btn {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: #abd1c6;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-modal-btn:hover {
  background: #f0f0f0;
  color: #004643;
  transform: scale(1.1);
}

.forgot-password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.forgot-password-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.reset-btn {
  flex: 1;
  background: linear-gradient(135deg, #004643, #006b5d);
  color: #fffffe;
  border: none;
  padding: 1rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.3);
}

.reset-btn:hover {
  background: linear-gradient(135deg, #006b5d, #004643);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 70, 67, 0.4);
}

.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  flex: 1;
  background: transparent;
  color: #abd1c6;
  border: 2px solid #abd1c6;
  padding: 1rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
}

.cancel-btn:hover {
  background: #abd1c6;
  color: #004643;
  transform: translateY(-2px);
}

/* رسالة النجاح */
.success-message {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(21, 87, 36, 0.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .login-container {
    padding: 0.5rem;
  }

  .login-card {
    padding: 2rem;
    max-width: 100%;
  }

  .cafe-logo .avie-logo {
    width: 80px;
    height: 80px;
  }

  .login-header p {
    font-size: 1.3rem;
  }

  .user-type-selector {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .user-type-card {
    padding: 1.25rem 1rem;
  }

  .forgot-password-container {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem;
  }

  .cafe-logo .avie-logo {
    width: 70px;
    height: 70px;
  }

  .login-header p {
    font-size: 1.2rem;
  }

  .input-wrapper input {
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    font-size: 0.95rem;
  }

  .login-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* ===== تصميم صفحة العملاء المحدث - كامل الشاشة ===== */

/* إعداد عام للتصميم المتجاوب */
.customer-app-redesigned *,
.customer-app-redesigned *::before,
.customer-app-redesigned *::after {
  box-sizing: border-box;
}

/* التطبيق الرئيسي */
.customer-app-redesigned {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: "Almarai", sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* الهيدر الحديث */
.modern-header {
  background: linear-gradient(135deg, #004643 0%, #006b5d 50%, #004643 100%);
  color: white;
  padding: 1.5rem 0;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  border-bottom: 3px solid #abd1c6;
}

.header-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80px;
  box-sizing: border-box;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.main-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-logo .avie-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.main-logo .avie-logo:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
}

.main-logo .avie-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
}

.brand-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.25rem;
}

.brand-text p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.95;
  color: #abd1c6;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 0 0 auto;
}

.cart-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 0.75rem 1.25rem;
  border-radius: 30px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cart-preview:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cart-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 0.4rem 0.7rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 700;
  min-width: 2rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.cart-amount {
  font-weight: 600;
  font-size: 1rem;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.staff-btn {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.1)
  );
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 30px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.staff-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0.15)
  );
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* المحتوى الرئيسي */
.main-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
}

/* قسم الترحيب */
.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem 1rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  width: 100%;
}

.welcome-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #004643);
}

.welcome-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #004643, #007f73);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

/* تنقل القائمة */
.menu-navigation {
  margin-bottom: 2rem;
  width: 100%;
}

/* محتوى القائمة */
.menu-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: none;
  margin: 0;
}

.category-section {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 2rem;
  box-sizing: border-box;
}

.category-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  width: 100%;
}

.section-header h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: #004643;
  position: relative;
}

.section-divider {
  flex: 1;
  height: 3px;
  background: linear-gradient(90deg, #004643, transparent);
  border-radius: 2px;
}

/* ===== شبكة المنتجات الموحدة ===== */
.products-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 1.5rem !important;
  width: 100% !important;
  padding: 1rem !important;
  box-sizing: border-box !important;
}

/* زر السلة العائم */
.floating-cart-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #004643, #004643);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 25px;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 200px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.floating-cart-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(52, 152, 219, 0.4);
  background: linear-gradient(135deg, #004643, #004643);
}

.cart-icon-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.cart-count-badge {
  background: #e74c3c;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
  min-width: 1.5rem;
  text-align: center;
}

.cart-text {
  font-weight: 600;
  font-size: 1rem;
}

.cart-total-amount {
  font-weight: 700;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 15px;
}

/* نافذة الطلب المحسنة */
.checkout-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
  padding: 1rem;
}

.checkout-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.modal-content {
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* ملخص الطلب */
.order-summary {
  margin-bottom: 2rem;
}

.order-summary h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.order-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.order-totals {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 2px solid #e9ecef;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 1rem;
}

.final-total {
  background: linear-gradient(135deg, #004643, #004643);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  font-weight: 700;
  font-size: 1.2rem;
  margin-top: 1rem;
}

/* معلومات العميل */
.customer-info {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* أزرار الإجراءات */
.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.confirm-order-btn {
  flex: 2;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
}

.confirm-order-btn:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.cancel-btn {
  flex: 1;
  background: #95a5a6;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 15px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Almarai", sans-serif;
}

.cancel-btn:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

/* تحسين التبويبات */
.category-tabs-container {
  background: white !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  overflow: hidden !important;
  padding: 0.5rem !important;
  width: 100% !important;
  margin: 0px !important;
}

.category-tabs {
  gap: 0.5rem !important;
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
}

.category-tab {
  background: transparent !important;
  color: #004643 !important;
  border: none !important;
  border-radius: 15px !important;
  padding: 1rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  flex: 1 !important;
  min-width: 120px !important;
  text-align: center !important;
}

.category-tab:hover {
  background: rgba(52, 152, 219, 0.1) !important;
  color: #004643 !important;
  transform: translateY(-2px) !important;
}

.category-tab.active {
  background: linear-gradient(135deg, #004643, #004643) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3) !important;
  transform: translateY(-2px) !important;
  display: flex !important; /* يجعل العنصر مرنًا */
  justify-content: center !important; /* يوسّط النص أفقيًا */
  align-items: center !important; /* يوسّط النص عموديًا */
  text-align: center !important; /* طبقة حماية إضافية */
}

.category-tab.active::before {
  display: none !important;
}

/* ===== تصميم كروت المنتجات الموحد ===== */
.menu-item-card {
  background: white !important;
  border-radius: 16px !important;
  padding: 0 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  border: 1px solid #f0f0f0 !important;
  cursor: pointer !important;
  overflow: hidden !important;
  position: relative !important;
  width: 100% !important;
  height: auto !important;
  min-height: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

.menu-item-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(0, 70, 67, 0.12) !important;
  border-color: #004643 !important;
}

/* صورة المنتج */
.menu-item-image {
  width: 100% !important;
  height: 0 !important;
  padding-bottom: 75% !important; /* نسبة 4:3 */
  overflow: hidden !important;
  position: relative !important;
  flex-shrink: 0 !important;
}

.menu-item-image img {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: all 0.3s ease !important;
}

.menu-item-card:hover .menu-item-image img {
  transform: scale(1.05) !important;
}

/* محتوى البطاقة */
.item-content {
  padding: 1rem !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  gap: 0.75rem !important;
}

.item-info {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  flex: 1 !important;
}

.item-info h5 {
  margin: 0 !important;
  color: #2c3e50 !important;
  font-size: 1.1rem !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  text-align: right !important;
}

.item-info p {
  margin: 0 !important;
  color: #7f8c8d !important;
  font-size: 0.85rem !important;
  line-height: 1.4 !important;
  text-align: right !important;
  opacity: 0.8 !important;
}

/* تذييل البطاقة */
.item-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-top: auto !important;
  padding-top: 0.5rem !important;
}

.item-price {
  color: #004643 !important;
  font-weight: 700 !important;
  font-size: 1.2rem !important;
  text-align: right !important;
  margin: 0 !important;
}

/* زر الإضافة */
.add-to-cart-btn {
  background: #004643 !important;
  color: white !important;
  border: none !important;
  padding: 0.625rem 1rem !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.2) !important;
  text-align: center !important;
}

.add-to-cart-btn:hover {
  background: #006b5d !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 70, 67, 0.3) !important;
}

/* تحسين السلة */
.floating-cart {
  background: white !important;
  border-radius: 20px !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  overflow: hidden !important;
}

.cart-header {
  background: linear-gradient(135deg, #3498db, #2ecc71) !important;
  color: white !important;
  padding: 1.5rem !important;
  margin: 0 !important;
  border-radius: 0 !important;
}

.cart-title h3 {
  color: white !important;
  font-size: 1.3rem !important;
  font-weight: 700 !important;
  margin: 0 !important;
}

.cart-toggle {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 15px !important;
  padding: 0.5rem 1rem !important;
}

.cart-count {
  background: #e74c3c !important;
  color: white !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 12px !important;
  font-size: 0.8rem !important;
  font-weight: 700 !important;
  min-width: 1.5rem !important;
  text-align: center !important;
}

.toggle-text {
  color: white !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
}

/* تصميم متجاوب للتصميم الجديد */
@media (max-width: 1400px) {
  .header-content {
    max-width: 1200px;
    padding: 0 1.5rem;
  }

  .main-container {
    max-width: 1200px;
    padding: 1rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 1200px) {
  .header-content {
    max-width: 100%;
    padding: 0 1rem;
  }

  .main-container {
    max-width: 100%;
    padding: 1rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem;
  }

  .category-section {
    padding: 1rem;
  }

  .section-header h3 {
    font-size: 1.6rem;
  }

  .main-logo .avie-logo {
    width: 70px;
    height: 70px;
  }

  .welcome-content h2 {
    font-size: 2.2rem;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
    min-height: auto;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .brand-section {
    justify-content: center;
  }

  .header-actions {
    gap: 1rem;
  }

  .main-container {
    padding: 0.75rem;
  }

  .welcome-content h2 {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }

  .floating-cart-button {
    bottom: 1.5rem;
    right: 1.5rem;
    min-width: 180px;
  }

  .checkout-modal {
    max-width: 90%;
    margin: 1rem;
  }

  .category-tab {
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .main-logo .avie-logo {
    width: 65px;
    height: 65px;
  }
}

@media (max-width: 1024px) {
  .modern-header {
    padding: 1.25rem 0;
  }

  .header-content {
    padding: 0 1.5rem;
    min-height: 70px;
  }

  .brand-logo {
    width: 60px;
    height: 60px;
  }

  .brand-text h1 {
    font-size: 2.2rem;
  }

  .brand-text p {
    font-size: 0.9rem;
  }

  .header-actions {
    gap: 1.5rem;
  }

  .cart-preview {
    padding: 0.6rem 1rem;
  }

  .staff-btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .modern-header {
    padding: 1rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0 1rem;
    min-height: auto;
  }

  .brand-section {
    justify-content: center;
    width: 100%;
  }

  .brand-logo {
    width: 55px;
    height: 55px;
  }

  .brand-text h1 {
    font-size: 2rem;
  }

  .brand-text p {
    font-size: 0.85rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
    gap: 1rem;
  }

  .cart-preview {
    padding: 0.5rem 1rem;
    gap: 0.75rem;
  }

  .cart-badge {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }

  .cart-amount {
    font-size: 0.9rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .brand-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .main-logo .avie-logo {
    width: 60px;
    height: 60px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .staff-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
  }

  .main-container {
    padding: 0.5rem;
  }

  .welcome-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .welcome-content h2 {
    font-size: 1.8rem;
  }

  .welcome-content p {
    font-size: 1rem;
  }

  .category-section {
    padding: 1rem;
  }

  .section-header h3 {
    font-size: 1.4rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }

  .floating-cart-button {
    bottom: 1rem;
    right: 0.5rem;
    left: 0.5rem;
    min-width: auto;
    padding: 1rem;
    width: calc(100% - 1rem);
  }

  .cart-text {
    font-size: 0.9rem;
  }

  .cart-total-amount {
    font-size: 1rem;
  }

  .checkout-modal {
    max-width: 95%;
    margin: 0.5rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
  }

  .category-tab {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.85rem !important;
    min-width: 100px !important;
  }
}

@media (max-width: 480px) {
  .modern-header {
    padding: 0.75rem 0;
  }

  .header-content {
    padding: 0 0.75rem;
    gap: 1rem;
  }

  .brand-section {
    gap: 1rem;
  }

  .brand-logo {
    width: 50px;
    height: 50px;
  }

  .brand-text h1 {
    font-size: 1.8rem;
  }

  .brand-text p {
    font-size: 0.8rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .cart-preview {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
  }

  .cart-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.5rem;
  }

  .cart-amount {
    font-size: 0.85rem;
  }

  .staff-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    gap: 0.4rem;
    width: 100%;
    justify-content: center;
  }

  .main-container {
    padding: 0.25rem;
  }

  .welcome-section {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .welcome-content h2 {
    font-size: 1.3rem;
  }

  .welcome-content p {
    font-size: 0.85rem;
  }

  .category-section {
    padding: 0.75rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .section-header h3 {
    font-size: 1.2rem;
  }

  .products-grid {
    grid-template-columns: 1fr !important;
    gap: 0.5rem;
  }

  .main-logo .avie-logo {
    width: 55px;
    height: 55px;
  }

  .welcome-content h2 {
    font-size: 1.5rem;
  }

  .welcome-content p {
    font-size: 0.9rem;
  }

  .floating-cart-button {
    padding: 0.75rem;
    gap: 0.5rem;
    bottom: 0.5rem;
    right: 0.25rem;
    left: 0.25rem;
    width: calc(100% - 0.5rem);
  }

  .cart-icon-wrapper {
    gap: 0.25rem;
  }

  .cart-text {
    font-size: 0.8rem;
  }

  .cart-total-amount {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
  }

  .checkout-modal {
    max-width: 100%;
    margin: 0;
    border-radius: 15px 15px 0 0;
    max-height: 95vh;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-content {
    padding: 0.75rem;
  }

  .category-tab {
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    min-width: 80px !important;
  }

  .category-tabs {
    gap: 0.25rem !important;
  }
}

/* ===== أنماط صفحة الأدمن المحسنة ===== */

/* Admin Container */
.admin-container {
  width: 100%;
  min-height: calc(100vh - 70px);
  padding: 2rem;
  background-color: #f9f5f1;
}

/* Admin Header */
.admin-header {
  background: linear-gradient(135deg, #004643 0%, #006b5d 50%, #abd1c6 100%);
  color: white;
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.admin-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
}

.admin-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Admin Tabs */
.admin-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  gap: 0.5rem;
}

.admin-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.admin-tab:hover {
  background: rgba(0, 70, 67, 0.05);
  color: #004643;
}

.admin-tab.active {
  background: #004643;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 70, 67, 0.3);
}

.admin-tab svg {
  font-size: 1.1rem;
}

/* Admin Actions */
.admin-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.add-product-btn,
.add-category-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  height: 42px;
  min-width: 140px;
  justify-content: center;
}

.add-product-btn:hover,
.add-category-btn:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.5);
}

/* Categories Display */
.categories-display {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.categories-display h2 {
  color: #004643;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-card:hover {
  border-color: #004643;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.1);
  transform: translateY(-2px);
}

.category-info h3 {
  color: #004643;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.category-info p {
  color: #666;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.category-desc {
  color: #888 !important;
  font-style: italic;
}

/* Product Cards Enhancement */
.product-card {
  position: relative;
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 70, 67, 0.12);
  border-color: #004643;
}

.product-image {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-details {
  flex: 1;
  margin-bottom: 1rem;
}

.product-name {
  color: #004643;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.product-name-en {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  font-style: italic;
}

.product-desc {
  color: #888;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
}

.product-price {
  color: #28a745;
  font-size: 1.3rem;
  font-weight: 700;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.edit-btn,
.delete-btn {
  background: none;
  border: 2px solid;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: all 0.3s ease;
}

.edit-btn {
  border-color: #007bff;
  color: #007bff;
}

.edit-btn:hover {
  background: #007bff;
  color: white;
  transform: scale(1.05);
}

.delete-btn {
  border-color: #dc3545;
  color: #dc3545;
}

.delete-btn:hover {
  background: #dc3545;
  color: white;
  transform: scale(1.05);
}

/* أزرار حذف الفئة مع المنتجات */
.delete-with-products-btn {
  background: linear-gradient(135deg, #dc3545, #8b0000);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 90px;
  height: 36px;
  justify-content: center;
}

.delete-with-products-btn:hover {
  background: linear-gradient(135deg, #8b0000, #660000);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.5);
}

.delete-with-products-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.delete-with-products-text {
  font-size: 0.65rem;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* تحسين زر الحذف العادي للفئات */
.category-actions .delete-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  min-width: 70px;
  height: 36px;
  justify-content: center;
}

.category-actions .delete-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.5);
}

.category-actions .delete-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

/* زر حذف جميع منتجات الفئة */
.delete-category-products-btn {
  background: linear-gradient(135deg, #fd7e14, #e55100);
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
  margin-right: 0.5rem;
  min-width: 80px;
  height: 36px;
  justify-content: center;
}

.delete-category-products-btn:hover {
  background: linear-gradient(135deg, #e55100, #bf360c);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(253, 126, 20, 0.5);
}

.delete-category-products-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.delete-text {
  font-size: 0.7rem;
  font-weight: 700;
}

/* زر إعادة تعيين البيانات */
.reset-data-btn {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
  height: 42px;
  min-width: 140px;
  justify-content: center;
}

.reset-data-btn:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.5);
}

.reset-data-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h3 {
  color: #004643;
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.empty-state p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

/* Modal Styles for Admin */
.add-product-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #004643 0%, #006b5d 50%, #abd1c6 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Form Styles */
.add-product-form {
  padding: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #004643;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #004643;
  box-shadow: 0 0 0 3px rgba(0, 70, 67, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
  text-align: center;
}

.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.image-preview {
  width: 100%;
  max-width: 200px;
  height: 150px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #e9ecef;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.submit-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: #218838;
  transform: translateY(-2px);
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex; /* تغيير من flex إلى inline-flex */
  justify-content: center; /* إضافة هذه الخاصية */
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-align: center; /* إضافة للتأكد من توسيط النص */
  min-width: max-content; /* لمنع تقطيع النص */
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* Responsive Design for Admin */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-header {
    padding: 1.5rem;
  }

  .admin-header h1 {
    font-size: 1.5rem;
  }

  .admin-tabs {
    flex-direction: column;
    gap: 0.25rem;
  }

  .admin-tab {
    justify-content: flex-start;
    padding: 0.75rem 1rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .product-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .edit-btn,
  .delete-btn {
    width: 100%;
    height: auto;
    padding: 0.75rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .add-product-form {
    padding: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .submit-btn,
  .cancel-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Welcome Section Enhancement */
.welcome-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-content h2 {
  color: #004643;
  margin-bottom: 0.5rem;
}

.welcome-content p {
  color: #666;
  margin-bottom: 1rem;
}

/* ===== كروت المنتجات المحسنة ===== */

/* Modern Menu Card */
.modern-menu-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(0, 70, 67, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modern-menu-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 70, 67, 0.15);
  border-color: #004643;
}

.modern-menu-card.adding {
  transform: scale(0.98);
  box-shadow: 0 5px 15px rgba(0, 70, 67, 0.2);
}

/* Card Image Container - محسن للصور المربعة 500x500 */
.card-image-container {
  position: relative;
  width: 100%;
  height: 250px; /* زيادة الارتفاع للصور المربعة */
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px 15px 0 0; /* زوايا مدورة للجزء العلوي فقط */

  /* تحسين عرض الصور عالية الجودة */
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* يحافظ على النسبة مع قص الزوائد */
  object-position: center; /* توسيط الصورة */
  transition: all 0.4s ease;
  filter: brightness(1.05) contrast(1.1); /* تحسين جودة الصورة */

  /* تحسين خاص للصور المربعة 500x500 */
  min-width: 100%;
  min-height: 100%;
  max-width: none; /* إزالة القيود على العرض */

  /* ضمان الوضوح الأمثل */
  image-orientation: from-image;
  color-interpolation-filters: sRGB;
}

.modern-menu-card:hover .card-image {
  transform: scale(1.08); /* تقليل التكبير قليلاً للصور عالية الجودة */
  filter: brightness(1.1) contrast(1.15) saturate(1.1); /* تحسين الألوان عند التمرير */
}

/* تحسين جودة الصور عالية الدقة 500x500 */
.card-image {
  image-rendering: -webkit-optimize-contrast; /* لـ Safari */
  image-rendering: crisp-edges; /* للمتصفحات الحديثة */
  image-rendering: auto; /* الافتراضي المحسن */

  /* تحسين الأداء */
  will-change: transform, filter; /* تحسين الأداء للتحويلات */
  backface-visibility: hidden; /* منع الوميض */

  /* تحسين جودة العرض */
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;

  /* تحسين الحدة للصور عالية الدقة */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Card Content */
.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-info {
  margin-bottom: 16px;
}

.product-title {
  color: #004643;
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.3;
  text-align: center;
}

.product-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Product Price Section */
.product-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.product-price {
  color: #004643;
  font-size: 1.4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #004643, #006b5d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.click-hint {
  color: #abd1c6;
  font-size: 0.8rem;
  font-weight: 500;
  opacity: 0;
  transition: all 0.3s ease;
  background: rgba(171, 209, 198, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.modern-menu-card:hover .click-hint {
  opacity: 1;
  color: #004643;
  background: rgba(171, 209, 198, 0.2);
}

/* Adding Overlay */
.adding-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 70, 67, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: fadeIn 0.3s ease;
}

.adding-feedback {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: white;
  text-align: center;
}

.success-icon {
  width: 50px;
  height: 50px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  animation: bounceIn 0.5s ease;
}

.adding-feedback span {
  font-size: 1.1rem;
  font-weight: 600;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Grid Layout for Menu Items - محسن للصور المربعة */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(300px, 1fr)
  ); /* زيادة العرض الأدنى */
  gap: 28px; /* زيادة المسافات */
  padding: 24px 0;
  max-width: 1400px; /* حد أقصى للعرض */
  margin: 0 auto; /* توسيط الشبكة */
}

/* Responsive Design - محسن للصور المربعة */

/* الأجهزة اللوحية الكبيرة */
@media (max-width: 1024px) {
  .menu-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    max-width: 1200px;
  }

  .card-image-container {
    height: 230px;
  }
}

/* الأجهزة اللوحية */
@media (max-width: 768px) {
  .modern-menu-card {
    border-radius: 16px;
  }

  .card-image-container {
    height: 200px; /* ارتفاع مناسب للصور المربعة */
    border-radius: 12px 12px 0 0;
  }

  .card-content {
    padding: 18px;
  }

  .product-title {
    font-size: 1.15rem;
  }

  .product-price {
    font-size: 1.25rem;
  }

  .menu-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 20px;
    padding: 20px 0;
  }
}

/* الهواتف الكبيرة */
@media (max-width: 600px) {
  .menu-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 18px;
    padding: 16px;
  }

  .card-image-container {
    height: 220px; /* الحفاظ على النسبة المربعة */
  }
}

/* الهواتف الصغيرة */
@media (max-width: 480px) {
  .menu-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 12px;
  }

  .card-image-container {
    height: 200px; /* مناسب للشاشات الصغيرة */
  }

  .card-content {
    padding: 16px;
  }

  .product-title {
    font-size: 1.1rem;
  }

  .product-price {
    font-size: 1.2rem;
  }
}

/* الشاشات الصغيرة جداً */
@media (max-width: 360px) {
  .card-image-container {
    height: 180px;
  }

  .card-content {
    padding: 14px;
  }
}

/* ===== أنماط ترتيب الفئات ===== */

/* Categories Display */
.categories-display {
  margin-top: 2rem;
}

.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.categories-header h2 {
  color: #004643;
  margin: 0;
  font-size: 1.5rem;
}

.categories-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.categories-info svg {
  color: #004643;
}

/* Categories List */
.categories-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.category-item:hover {
  border-color: #004643;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.1);
  transform: translateY(-2px);
}

/* Category Order Number */
.category-order {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #004643, #006b5d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-number {
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Category Info */
.category-info {
  flex: 1;
}

.category-info h3 {
  color: #004643;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.category-name-en {
  color: #666;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-style: italic;
}

.category-desc {
  color: #777;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Category Actions */
.category-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.order-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #004643;
  background: white;
  color: #004643;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.order-btn:hover:not(.disabled) {
  background: #004643;
  color: white;
  transform: scale(1.1);
}

.order-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  border-color: #ccc;
  color: #ccc;
}

/* ===== Language Switcher Styles ===== */

/* Language Switcher - Default */
.language-switcher {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  min-width: 200px;
}

.language-switcher-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #004643;
  font-weight: 600;
  font-size: 0.9rem;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.language-option:hover {
  border-color: #004643;
  background: #f8f9fa;
  transform: translateY(-1px);
}

.language-option.active {
  border-color: #004643;
  background: linear-gradient(135deg, #004643, #006b5d);
  color: white;
}

.lang-flag {
  font-size: 1.2rem;
}

.lang-name {
  flex: 1;
  text-align: start;
}

.active-indicator {
  color: #28a745;
  font-weight: bold;
}

.language-option.active .active-indicator {
  color: white;
}

/* Language Switcher - Compact */
.language-switcher.compact {
  position: relative;
  display: inline-block;
  background: transparent;
  padding: 0;
  box-shadow: none;
  border: none;
  min-width: auto;
}

.language-toggle-btn.compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.language-toggle-btn.compact:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.current-lang-flag {
  font-size: 1rem;
}

.chevron {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
}

.chevron.open {
  transform: rotate(180deg);
}

.language-dropdown.compact {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  min-width: 150px;
  z-index: 1000;
  overflow: hidden;
}

.language-dropdown.compact .language-option {
  border: none;
  border-radius: 0;
  margin: 0;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
}

.language-dropdown.compact .language-option:last-child {
  border-bottom: none;
}

.language-dropdown.compact .language-option:hover {
  background: #f8f9fa;
  transform: none;
}

.language-dropdown.compact .language-option.active {
  background: #004643;
  color: white;
}

/* RTL/LTR Support */
.lang-ar .language-switcher {
  direction: rtl;
}

.lang-en .language-switcher {
  direction: ltr;
}

.lang-en .language-dropdown.compact {
  left: 0;
  right: auto;
}

.lang-en .lang-name {
  text-align: left;
}

/* Responsive Design */
@media (max-width: 768px) {
  .language-switcher {
    min-width: 180px;
  }

  .language-toggle-btn.compact {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .language-dropdown.compact {
    min-width: 140px;
  }
}

/* ===== Multi-Language Support ===== */

/* Header Actions Layout */
.header-actions {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 100 !important;
}

/* Language Button */
.language-btn {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.8rem 1.5rem !important;
  background: #ffffff !important;
  border: 3px solid #2c5530 !important;
  border-radius: 25px !important;
  color: #2c5530 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-size: 1.1rem !important;
  font-weight: bold !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  margin-right: 1rem !important;
  min-width: 120px !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
}

.language-btn:hover {
  background: #2c5530;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(44, 85, 48, 0.4);
}

.language-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 15px rgba(44, 85, 48, 0.3);
}

/* English Language Styles */
.lang-en {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.lang-en .header-content {
  text-align: left;
}

.lang-en .header-text h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

.lang-en .header-text p {
  font-size: 1.1rem;
  font-weight: 400;
}

.lang-en .table-selector p {
  text-align: left;
}

.lang-en .welcome-content {
  text-align: left;
}

.lang-en .cart-title h3 {
  font-size: 1.1rem;
}

.lang-en .checkout-modal {
  text-align: left;
}

.lang-en .modal-header h2 {
  font-size: 1.5rem;
}

.lang-en .form-group label {
  text-align: center;
  display: block;
  margin-bottom: 0.5rem;
}

.lang-en .total-row {
  justify-content: space-between;
}

/* Arabic Language Styles (Default) */
.lang-ar {
  font-family: "Cairo", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.lang-ar .header-content {
  text-align: right;
}

.lang-ar .header-text h1 {
  font-size: 2.8rem;
  font-weight: 800;
}

.lang-ar .header-text p {
  font-size: 1.2rem;
  font-weight: 500;
}

.lang-ar .table-selector p {
  text-align: right;
}

.lang-ar .welcome-content {
  text-align: center;
}

.lang-ar .cart-title h3 {
  font-size: 1.2rem;
}

.lang-ar .checkout-modal {
  text-align: right;
}

.lang-ar .modal-header h2 {
  font-size: 1.6rem;
}

.lang-ar .form-group label {
  text-align: right;
  display: block;
  margin-bottom: 0.5rem;
}

.lang-ar .total-row {
  justify-content: space-between;
}

/* Button Styles for Different Languages */
.lang-en .refresh-btn,
.lang-en .checkout-button,
.lang-en .confirm-order-btn {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.lang-ar .refresh-btn,
.lang-ar .checkout-button,
.lang-ar .confirm-order-btn {
  font-weight: 700;
}

/* Category Tabs Language Support */
.lang-en .category-tab {
  font-weight: 600;
  text-transform: capitalize;
}

.lang-ar .category-tab {
  font-weight: 700;
}

/* Product Cards Language Support */
.lang-en .product-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
}

.lang-ar .product-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
}

.lang-en .product-card p {
  font-size: 0.9rem;
  line-height: 1.4;
}

.lang-ar .product-card p {
  font-size: 1rem;
  line-height: 1.6;
}

/* Responsive Multi-Language */
@media (max-width: 768px) {
  .lang-en .header-text h1 {
    font-size: 2rem;
  }

  .lang-ar .header-text h1 {
    font-size: 2.2rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .header-actions .refresh-btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .language-toggle-btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
  }
}

/* Empty Categories */
.empty-categories {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.empty-categories .empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-categories h3 {
  color: #004643;
  margin-bottom: 0.5rem;
}

.empty-categories p {
  margin: 0;
  font-size: 0.9rem;
}

/* Responsive Design for Categories */
@media (max-width: 768px) {
  .categories-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .category-item {
    padding: 1rem;
    gap: 0.75rem;
  }

  .category-order {
    width: 35px;
    height: 35px;
  }

  .order-number {
    font-size: 1rem;
  }

  .category-info h3 {
    font-size: 1.1rem;
  }

  .order-btn {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .category-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .category-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* Responsive Design for Header Refresh Button */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .refresh-btn {
    font-size: 0.8rem;
    padding: 0.6rem 1.2rem;
  }
}

@media (max-width: 480px) {
  .refresh-btn {
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    gap: 0.3rem;
  }
}

/* ===== FORCE HEADER BUTTONS VISIBILITY ===== */
/* تأكيد ظهور أزرار الهيدر */
.app-header .header-content {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 2rem !important;
  position: relative !important;
}

.app-header .header-content .header-actions {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
  position: relative !important;
  flex-shrink: 0 !important;
}

.app-header .header-content .header-actions .language-btn {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.8rem 1.5rem !important;
  background: #ffffff !important;
  border: 3px solid #2c5530 !important;
  border-radius: 25px !important;
  color: #2c5530 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-size: 1.1rem !important;
  font-weight: bold !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  min-width: 120px !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1001 !important;
}

.app-header .header-content .header-actions .refresh-btn {
  background: linear-gradient(135deg, #2c5530, #2d5a5a) !important;
  color: white !important;
  border: 3px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 15px !important;
  padding: 0.8rem 1.5rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  box-shadow: 0 4px 15px rgba(0, 70, 67, 0.2) !important;
  min-width: 160px !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1001 !important;
}
