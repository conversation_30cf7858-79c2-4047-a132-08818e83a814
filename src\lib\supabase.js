import { createClient } from "@supabase/supabase-js";

// Supabase configuration
const supabaseUrl =
  process.env.REACT_APP_SUPABASE_URL ||
  "https://tjivtrkbjsesulrthxpr.supabase.co";
const supabaseAnonKey =
  process.env.REACT_APP_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw";

// Validate configuration
if (
  !supabaseUrl ||
  supabaseUrl === "YOUR_SUPABASE_URL" ||
  !supabaseUrl.startsWith("http")
) {
  console.error("❌ Supabase URL is not configured properly");
}

if (!supabaseAnonKey || supabaseAnonKey === "YOUR_SUPABASE_ANON_KEY") {
  console.error("❌ Supabase Anon Key is not configured properly");
}

// Singleton pattern for Supabase client to avoid multiple instances
let supabaseInstance = null;

const createSupabaseClient = () => {
  if (supabaseInstance) {
    return supabaseInstance;
  }

  try {
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        storageKey: "avie-cafe-auth",
        storage: window.localStorage,
      },
    });
    console.log("✅ Supabase client created successfully");
    return supabaseInstance;
  } catch (error) {
    console.error("❌ Failed to create Supabase client:", error);
    // Create a mock client for development
    supabaseInstance = {
      from: () => ({
        select: () => ({
          data: [],
          error: new Error("Supabase not configured"),
        }),
        insert: () => ({
          data: null,
          error: new Error("Supabase not configured"),
        }),
        update: () => ({
          data: null,
          error: new Error("Supabase not configured"),
        }),
        delete: () => ({
          data: null,
          error: new Error("Supabase not configured"),
        }),
      }),
      auth: {
        getSession: () => ({ data: { session: null }, error: null }),
        onAuthStateChange: () => ({
          data: { subscription: { unsubscribe: () => {} } },
        }),
      },
    };
    return supabaseInstance;
  }
};

// Create the single instance
const supabase = createSupabaseClient();

// Export function to get the singleton instance
export const getSupabaseClient = () => {
  return supabaseInstance || createSupabaseClient();
};

export { supabase };

// Database table names
export const TABLES = {
  USERS: "users",
  PRODUCTS: "products",
  CATEGORIES: "categories",
  ORDERS: "orders",
  ORDER_ITEMS: "order_items",
  COMPLETE_ORDERS: "complete_orders", // الجدول الجديد الشامل
  SALES: "sales",
  WAITERS: "waiters",
};

// Helper functions for database operations
export const db = {
  // Users operations
  users: {
    async create(userData) {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .insert([userData])
        .select();
      return { data, error };
    },

    async getByUsername(username) {
      try {
        const { data, error } = await supabase
          .from(TABLES.USERS)
          .select("*")
          .eq("username", username)
          .maybeSingle();
        return { data, error };
      } catch (err) {
        console.error("Error in getByUsername:", err);
        return { data: null, error: err };
      }
    },

    async getByEmail(email) {
      try {
        const { data, error } = await supabase
          .from(TABLES.USERS)
          .select("*")
          .eq("email", email)
          .maybeSingle();
        return { data, error };
      } catch (err) {
        console.error("Error in getByEmail:", err);
        return { data: null, error: err };
      }
    },

    async updatePassword(userId, newPassword) {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .update({ password: newPassword })
        .eq("id", userId)
        .select();
      return { data, error };
    },
  },

  // Products operations
  products: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.PRODUCTS)
        .select(
          `
          *,
          categories (
            id,
            name,
            name_en
          )
        `
        )
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async create(productData) {
      const { data, error } = await supabase
        .from(TABLES.PRODUCTS)
        .insert([productData])
        .select();
      return { data, error };
    },

    async update(id, productData) {
      const { data, error } = await supabase
        .from(TABLES.PRODUCTS)
        .update(productData)
        .eq("id", id)
        .select();
      return { data, error };
    },

    async delete(id) {
      const { data, error } = await supabase
        .from(TABLES.PRODUCTS)
        .delete()
        .eq("id", id);
      return { data, error };
    },
  },

  // Categories operations
  categories: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.CATEGORIES)
        .select("*")
        .order("name");
      return { data, error };
    },

    async create(categoryData) {
      const { data, error } = await supabase
        .from(TABLES.CATEGORIES)
        .insert([categoryData])
        .select();
      return { data, error };
    },

    async update(id, categoryData) {
      const { data, error } = await supabase
        .from(TABLES.CATEGORIES)
        .update(categoryData)
        .eq("id", id)
        .select();
      return { data, error };
    },

    async delete(id) {
      const { data, error } = await supabase
        .from(TABLES.CATEGORIES)
        .delete()
        .eq("id", id);
      return { data, error };
    },
  },

  // Orders operations
  orders: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.ORDERS)
        .select(
          `
          *,
          order_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          )
        `
        )
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async create(orderData) {
      const { data, error } = await supabase
        .from(TABLES.ORDERS)
        .insert([orderData])
        .select();
      return { data, error };
    },

    async update(id, orderData) {
      const { data, error } = await supabase
        .from(TABLES.ORDERS)
        .update(orderData)
        .eq("id", id)
        .select();
      return { data, error };
    },

    async updateStatus(id, status) {
      const { data, error } = await supabase
        .from(TABLES.ORDERS)
        .update({ status, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select();
      return { data, error };
    },
  },

  // Order items operations
  orderItems: {
    async create(orderItems) {
      const { data, error } = await supabase
        .from(TABLES.ORDER_ITEMS)
        .insert(orderItems)
        .select();
      return { data, error };
    },
  },

  // Complete orders operations (الجدول الجديد الشامل)
  completeOrders: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.COMPLETE_ORDERS)
        .select("*")
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async create(orderItems) {
      const { data, error } = await supabase
        .from(TABLES.COMPLETE_ORDERS)
        .insert(orderItems)
        .select();
      return { data, error };
    },

    async updateStatus(orderId, status) {
      const { data, error } = await supabase
        .from(TABLES.COMPLETE_ORDERS)
        .update({ status, updated_at: new Date().toISOString() })
        .eq("order_id", orderId)
        .select();
      return { data, error };
    },

    async getByOrderId(orderId) {
      const { data, error } = await supabase
        .from(TABLES.COMPLETE_ORDERS)
        .select("*")
        .eq("order_id", orderId)
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async getByTableNumber(tableNumber) {
      const { data, error } = await supabase
        .from(TABLES.COMPLETE_ORDERS)
        .select("*")
        .eq("table_number", tableNumber)
        .order("created_at", { ascending: false });
      return { data, error };
    },
  },

  // Sales operations
  sales: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.SALES)
        .select("*")
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async create(saleData) {
      const { data, error } = await supabase
        .from(TABLES.SALES)
        .insert([saleData])
        .select();
      return { data, error };
    },

    async getByWaiter(waiterName) {
      const { data, error } = await supabase
        .from(TABLES.SALES)
        .select("*")
        .eq("waiter_name", waiterName)
        .order("created_at", { ascending: false });
      return { data, error };
    },

    async getByDateRange(startDate, endDate) {
      const { data, error } = await supabase
        .from(TABLES.SALES)
        .select("*")
        .gte("created_at", startDate)
        .lte("created_at", endDate)
        .order("created_at", { ascending: false });
      return { data, error };
    },
  },

  // Waiters operations
  waiters: {
    async getAll() {
      const { data, error } = await supabase
        .from(TABLES.WAITERS)
        .select("*")
        .order("name");
      return { data, error };
    },

    async create(waiterData) {
      const { data, error } = await supabase
        .from(TABLES.WAITERS)
        .insert([waiterData])
        .select();
      return { data, error };
    },
  },
};

// Real-time subscriptions
export const subscriptions = {
  // Subscribe to orders changes
  subscribeToOrders(callback) {
    return supabase
      .channel("orders")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: TABLES.ORDERS },
        callback
      )
      .subscribe();
  },

  // Subscribe to products changes
  subscribeToProducts(callback) {
    return supabase
      .channel("products")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: TABLES.PRODUCTS },
        callback
      )
      .subscribe();
  },

  // Unsubscribe from channel
  unsubscribe(subscription) {
    return supabase.removeChannel(subscription);
  },
};

export default supabase;
