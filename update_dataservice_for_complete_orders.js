// تحديث dataService.js لاستخدام الجدول الشامل الجديد
// هذا الملف يحتوي على الكود المحدث لحفظ وجلب الطلبات

// إضافة هذه الدوال إلى dataService.js

// حفظ الطلب في الجدول الشامل
async saveOrderToCompleteTable(orderData) {
  if (!this.isSupabaseAvailable()) {
    console.warn("⚠️ Supabase not available, saving order locally");
    return this.saveLocalOrder(orderData);
  }

  try {
    console.log("💾 Saving order to complete_orders table...");
    console.log("📦 Order data:", orderData);

    // إنشاء معرف فريد للطلب
    const orderId = orderData.id || this.generateUUID();
    
    // تحضير بيانات كل منتج في الطلب
    const orderItems = orderData.items.map((item) => ({
      order_id: orderId,
      table_number: orderData.tableNumber,
      customer_name: orderData.customerName || '',
      notes: orderData.notes || '',
      twitter_handle: orderData.twitterHandle || null,
      
      // معلومات المنتج
      product_id: item.productId || item.id,
      product_name: item.name || 'منتج غير محدد',
      product_description: item.desc || '',
      unit_price: item.price || 0,
      quantity: item.qty || item.quantity || 1,
      
      // معلومات الضريبة (سيتم حسابها تلقائياً بواسطة trigger)
      tax_rate: 15.00, // 15% ضريبة
      
      // معلومات الطلب
      status: orderData.status || 'pending',
      assigned_waiter: orderData.assignedWaiter || null,
      waiter_id: orderData.waiterId || null,
      payment_method: orderData.paymentMethod || null,
      is_paid: orderData.isPaid || false
    }));

    console.log("📋 Order items to save:", orderItems);

    // حفظ جميع عناصر الطلب
    const { data: savedItems, error } = await supabase
      .from('complete_orders')
      .insert(orderItems)
      .select();

    if (error) {
      console.error("❌ Error saving to complete_orders:", error);
      throw error;
    }

    console.log("✅ Order saved successfully:", savedItems?.length || 0, "items");
    
    // إرجاع معلومات الطلب
    return {
      id: orderId,
      tableNumber: orderData.tableNumber,
      customerName: orderData.customerName,
      items: savedItems,
      total: savedItems.reduce((sum, item) => sum + parseFloat(item.total_with_tax), 0)
    };

  } catch (error) {
    console.error("❌ Error saving order:", error);
    throw error;
  }
}

// جلب الطلبات من الجدول الشامل
async getOrdersFromCompleteTable() {
  if (!this.isSupabaseAvailable()) {
    console.warn("⚠️ Supabase not available, using local orders");
    return this.getLocalOrders();
  }

  try {
    console.log("🔍 Fetching orders from complete_orders table...");

    const { data, error } = await supabase
      .from('complete_orders')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error("❌ Error fetching orders:", error);
      throw error;
    }

    console.log("📊 Raw data from complete_orders:", data?.length || 0, "items");

    // تجميع البيانات حسب order_id
    const ordersMap = {};
    
    data?.forEach(item => {
      const orderId = item.order_id;
      
      if (!ordersMap[orderId]) {
        ordersMap[orderId] = {
          id: orderId,
          tableNumber: item.table_number,
          customerName: item.customer_name,
          notes: item.notes,
          twitterHandle: item.twitter_handle,
          status: item.status,
          assignedWaiter: item.assigned_waiter,
          waiterId: item.waiter_id,
          paymentMethod: item.payment_method,
          isPaid: item.is_paid,
          timestamp: item.created_at,
          items: [],
          subtotal: 0,
          tax: 0,
          total: 0
        };
      }

      // إضافة المنتج للطلب
      ordersMap[orderId].items.push({
        id: item.product_id,
        name: item.product_name,
        desc: item.product_description,
        price: parseFloat(item.unit_price),
        qty: item.quantity,
        itemTotal: parseFloat(item.item_total),
        taxAmount: parseFloat(item.tax_amount),
        totalWithTax: parseFloat(item.total_with_tax)
      });

      // تحديث المجاميع
      ordersMap[orderId].subtotal += parseFloat(item.item_total);
      ordersMap[orderId].tax += parseFloat(item.tax_amount);
      ordersMap[orderId].total += parseFloat(item.total_with_tax);
    });

    const orders = Object.values(ordersMap);
    console.log("📦 Processed orders:", orders.length);

    return orders;

  } catch (error) {
    console.error("❌ Error fetching orders:", error);
    console.warn("🔄 Falling back to local orders");
    return this.getLocalOrders();
  }
}

// تحديث حالة الطلب في الجدول الشامل
async updateOrderStatusInCompleteTable(orderId, status, additionalData = {}) {
  if (!this.isSupabaseAvailable()) {
    console.warn("⚠️ Supabase not available, updating order status locally");
    return this.updateLocalOrderStatus(orderId, status, additionalData);
  }

  try {
    console.log("🔄 Updating order status:", orderId, "to", status);

    const updateData = { 
      status, 
      ...additionalData,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('complete_orders')
      .update(updateData)
      .eq('order_id', orderId)
      .select();

    if (error) {
      console.error("❌ Error updating order status:", error);
      throw error;
    }

    console.log("✅ Order status updated:", data?.length || 0, "items");
    return data;

  } catch (error) {
    console.error("❌ Error updating order status:", error);
    throw error;
  }
}

// دالة مساعدة لإنشاء UUID
generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// تحديث الدوال الموجودة لاستخدام الجدول الجديد
// استبدل هذه الدوال في dataService.js:

/*
// استبدل saveOrder بـ:
async saveOrder(orderData) {
  return this.saveOrderToCompleteTable(orderData);
}

// استبدل getOrders بـ:
async getOrders() {
  return this.getOrdersFromCompleteTable();
}

// استبدل updateOrderStatus بـ:
async updateOrderStatus(orderId, status, additionalData = {}) {
  return this.updateOrderStatusInCompleteTable(orderId, status, additionalData);
}
*/

// استعلامات مفيدة للتقارير
const reportQueries = {
  // تقرير المبيعات اليومية
  dailySales: `
    SELECT 
      DATE(created_at) as date,
      COUNT(DISTINCT order_id) as total_orders,
      COUNT(*) as total_items,
      SUM(item_total) as subtotal,
      SUM(tax_amount) as total_tax,
      SUM(total_with_tax) as total_sales
    FROM complete_orders 
    WHERE DATE(created_at) = CURRENT_DATE
    GROUP BY DATE(created_at);
  `,

  // أكثر المنتجات مبيعاً
  topProducts: `
    SELECT 
      product_name,
      COUNT(*) as times_ordered,
      SUM(quantity) as total_quantity,
      SUM(total_with_tax) as total_revenue
    FROM complete_orders 
    WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
    GROUP BY product_name
    ORDER BY total_revenue DESC
    LIMIT 10;
  `,

  // تقرير العملاء
  customerReport: `
    SELECT 
      customer_name,
      COUNT(DISTINCT order_id) as total_orders,
      SUM(total_with_tax) as total_spent,
      MAX(created_at) as last_order
    FROM complete_orders 
    WHERE customer_name != ''
    GROUP BY customer_name
    ORDER BY total_spent DESC;
  `
};

console.log("📋 تم تحضير الكود المحدث لاستخدام الجدول الشامل!");
console.log("🔧 استبدل الدوال في dataService.js بالدوال الجديدة");
console.log("📊 استعلامات التقارير متوفرة في reportQueries");
