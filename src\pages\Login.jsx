import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  faCoffee,
  faConciergeBell,
  faUserTie,
  faEnvelope,
  faIdCard,
} from "@fortawesome/free-solid-svg-icons";
import { useAuth } from "../components/AuthProvider";
import AvieLogo from "../components/AvieLogo";
import { db } from "../lib/supabase";

const Login = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    fullName: "",
    userType: "waiter", // default
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [resetMessage, setResetMessage] = useState("");

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    setError("");
  };

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setResetMessage("");

    try {
      // البحث عن المستخدم بالبريد الإلكتروني في Supabase
      const { data: foundUser, error } = await db.users.getByEmail(resetEmail);

      if (error || !foundUser) {
        setError("البريد الإلكتروني غير موجود");
        return;
      }

      // في التطبيق الحقيقي، سيتم إرسال رابط إعادة تعيين كلمة المرور
      setResetMessage(
        "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني"
      );
      setResetEmail("");

      // العودة لصفحة تسجيل الدخول بعد 3 ثوان
      setTimeout(() => {
        setShowForgotPassword(false);
        setResetMessage("");
      }, 3000);
    } catch (error) {
      setError("حدث خطأ، يرجى المحاولة مرة أخرى");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      if (isLogin) {
        // تسجيل الدخول - محاولة Supabase أولاً، ثم المستخدمين الافتراضيين
        try {
          let userData = null;
          let isFromSupabase = false;

          // محاولة البحث في Supabase أولاً
          try {
            // البحث بالبريد الإلكتروني أولاً
            if (formData.username.includes("@")) {
              const { data, error } = await db.users.getByEmail(
                formData.username
              );
              if (!error && data) {
                userData = data;
                isFromSupabase = true;
              }
            } else {
              // البحث باسم المستخدم
              const { data, error } = await db.users.getByUsername(
                formData.username
              );
              if (!error && data) {
                userData = data;
                isFromSupabase = true;
              }
            }
          } catch (supabaseError) {
            console.warn(
              "Supabase not available, trying default users:",
              supabaseError
            );
          }

          // إذا لم نجد في Supabase، نجرب المستخدمين الافتراضيين
          if (!userData) {
            const defaultUsers = [
              {
                id: "admin-1",
                username: "admin",
                email: "<EMAIL>",
                password: "admin123",
                name: "مدير النظام",
                type: "admin",
              },
              {
                id: "waiter-1",
                username: "waiter",
                email: "<EMAIL>",
                password: "waiter123",
                name: "نادل",
                type: "waiter",
              },
              {
                id: "barista-1",
                username: "barista",
                email: "<EMAIL>",
                password: "barista123",
                name: "باريستا",
                type: "barista",
              },
            ];

            userData = defaultUsers.find(
              (user) =>
                user.username === formData.username ||
                user.email === formData.username
            );
          }

          if (!userData) {
            setError("البريد الإلكتروني أو اسم المستخدم غير موجود");
            return;
          }

          if (userData.password !== formData.password) {
            setError("كلمة المرور غير صحيحة");
            return;
          }

          // نجح تسجيل الدخول
          const userInfo = {
            id: userData.id,
            username: userData.username,
            name: userData.full_name || userData.name,
            email: userData.email,
            type: userData.user_type || userData.type,
            isFromSupabase,
          };

          login(userInfo);

          // توجيه المستخدم للوحة التحكم
          navigate("/dashboard");
        } catch (dbError) {
          console.error("Database error:", dbError);
          setError("حدث خطأ في الاتصال بقاعدة البيانات");
        }
      } else {
        // التسجيل (إضافة مستخدم جديد)

        // التحقق من صحة البيانات
        if (!formData.fullName.trim()) {
          setError("الاسم الكامل مطلوب");
          return;
        }

        if (!formData.email.trim()) {
          setError("البريد الإلكتروني مطلوب");
          return;
        }

        // التحقق من صحة البريد الإلكتروني
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
          setError("البريد الإلكتروني غير صحيح");
          return;
        }

        if (formData.password.length < 6) {
          setError("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
          return;
        }

        try {
          // التحقق من حالة Supabase أولاً
          let isSupabaseAvailable = false;

          try {
            // اختبار الاتصال بـ Supabase
            const testConnection = await db.users.getByUsername(
              "test_connection_check"
            );
            isSupabaseAvailable = true; // إذا لم يحدث خطأ، فـ Supabase متاح
            console.log("✅ Supabase is available for user creation");
          } catch (connectionError) {
            console.warn("⚠️ Supabase not available:", connectionError);
            isSupabaseAvailable = false;
          }

          if (isSupabaseAvailable) {
            // Supabase متاح - نقوم بالتحقق والإنشاء
            try {
              // التحقق من عدم وجود اسم المستخدم
              const { data: existingUsername } = await db.users.getByUsername(
                formData.username
              );
              if (existingUsername) {
                setError("اسم المستخدم موجود بالفعل");
                return;
              }

              // التحقق من عدم وجود البريد الإلكتروني
              const { data: existingEmail } = await db.users.getByEmail(
                formData.email
              );
              if (existingEmail) {
                setError("البريد الإلكتروني مستخدم بالفعل");
                return;
              }

              // إنشاء المستخدم الجديد في Supabase
              const newUser = {
                username: formData.username,
                email: formData.email,
                password: formData.password,
                full_name: formData.fullName, // استخدام full_name بدلاً من name
                user_type: formData.userType, // استخدام user_type بدلاً من type
                created_at: new Date().toISOString(),
              };

              const { error } = await db.users.create(newUser);

              if (error) {
                console.error("Supabase creation error:", error);
                throw error;
              }

              alert(
                "✅ تم إنشاء الحساب بنجاح في قاعدة البيانات! يرجى تسجيل الدخول"
              );
              console.log("✅ User created successfully in Supabase");
            } catch (supabaseError) {
              console.error("Error creating user in Supabase:", supabaseError);
              setError(
                "حدث خطأ في إنشاء الحساب في قاعدة البيانات. يرجى المحاولة مرة أخرى."
              );
              return;
            }
          } else {
            // Supabase غير متاح - إنشاء محلي
            console.warn("Creating user locally - Supabase not available");
            alert(
              "⚠️ قاعدة البيانات غير متاحة حالياً.\n\nتم إنشاء الحساب محلياً للاختبار فقط.\n\nملاحظة: الحساب متاح فقط في هذه الجلسة."
            );
          }

          setIsLogin(true);
          setFormData({
            username: "",
            email: "",
            password: "",
            fullName: "",
            userType: "waiter",
          });
        } catch (dbError) {
          console.error("Database error:", dbError);
          setError("حدث خطأ في إنشاء الحساب");
        }
      }
    } catch (error) {
      setError("حدث خطأ، يرجى المحاولة مرة أخرى");
    } finally {
      setLoading(false);
    }
  };

  const getUserTypeIcon = (type) => {
    switch (type) {
      case "barista":
        return faCoffee;
      case "waiter":
        return faConciergeBell;
      case "admin":
        return faUserTie;
      default:
        return faUser;
    }
  };

  const getUserTypeLabel = (type) => {
    switch (type) {
      case "barista":
        return "باريستا";
      case "waiter":
        return "نادل";
      case "admin":
        return "مدير";
      default:
        return "مستخدم";
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="cafe-logo">
            <AvieLogo size={100} />
          </div>
          <p>{isLogin ? "تسجيل الدخول" : "إنشاء حساب جديد"}</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          {error && <div className="error-message">{error}</div>}

          {!isLogin && (
            <div className="form-group">
              <label>الاسم الكامل</label>
              <div className="input-wrapper">
                <FontAwesomeIcon icon={faIdCard} className="input-icon" />
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  placeholder="أدخل اسمك الكامل"
                  required
                />
              </div>
            </div>
          )}

          <div className="form-group">
            <label>
              {isLogin ? "البريد الإلكتروني أو اسم المستخدم" : "اسم المستخدم"}
            </label>
            <div className="input-wrapper">
              <FontAwesomeIcon
                icon={isLogin ? faEnvelope : faUser}
                className="input-icon"
              />
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder={
                  isLogin
                    ? "أدخل البريد الإلكتروني أو اسم المستخدم"
                    : "أدخل اسم المستخدم"
                }
                required
              />
            </div>
          </div>

          {!isLogin && (
            <div className="form-group">
              <label>البريد الإلكتروني</label>
              <div className="input-wrapper">
                <FontAwesomeIcon icon={faEnvelope} className="input-icon" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="أدخل البريد الإلكتروني"
                  required
                />
              </div>
            </div>
          )}

          <div className="form-group">
            <label>كلمة المرور</label>
            <div className="input-wrapper">
              <FontAwesomeIcon icon={faLock} className="input-icon" />
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="أدخل كلمة المرور"
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                <FontAwesomeIcon icon={showPassword ? faEyeSlash : faEye} />
              </button>
            </div>
          </div>

          {!isLogin && (
            <div className="form-group">
              <label>نوع الوظيفة</label>
              <div className="user-type-selector">
                {["waiter", "barista"].map((type) => (
                  <label key={type} className="user-type-option">
                    <input
                      type="radio"
                      name="userType"
                      value={type}
                      checked={formData.userType === type}
                      onChange={handleInputChange}
                    />
                    <div className="user-type-card">
                      <FontAwesomeIcon icon={getUserTypeIcon(type)} />
                      <span>{getUserTypeLabel(type)}</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          <button type="submit" className="login-btn" disabled={loading}>
            {loading
              ? "جاري التحميل..."
              : isLogin
              ? "تسجيل الدخول"
              : "إنشاء الحساب"}
          </button>

          <div className="form-footer">
            <button
              type="button"
              className="toggle-form-btn"
              onClick={() => {
                setIsLogin(!isLogin);
                setError("");
                setFormData({
                  username: "",
                  email: "",
                  password: "",
                  fullName: "",
                  userType: "waiter",
                });
              }}
            >
              {isLogin
                ? "ليس لديك حساب؟ إنشاء حساب جديد"
                : "لديك حساب؟ تسجيل الدخول"}
            </button>

            {isLogin && (
              <div className="forgot-password-container">
                <span
                  className="forgot-password-link"
                  onClick={() => setShowForgotPassword(true)}
                >
                  نسيت كلمة المرور؟
                </span>
              </div>
            )}
          </div>
        </form>
      </div>

      {/* نافذة نسيان كلمة المرور */}
      {showForgotPassword && (
        <div className="forgot-password-modal">
          <div className="forgot-password-content">
            <div className="forgot-password-header">
              <h3>إعادة تعيين كلمة المرور</h3>
              <button
                type="button"
                className="close-modal-btn"
                onClick={() => {
                  setShowForgotPassword(false);
                  setResetEmail("");
                  setError("");
                  setResetMessage("");
                }}
              >
                ×
              </button>
            </div>

            <form
              onSubmit={handleForgotPassword}
              className="forgot-password-form"
            >
              {error && <div className="error-message">{error}</div>}
              {resetMessage && (
                <div className="success-message">{resetMessage}</div>
              )}

              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <div className="input-wrapper">
                  <FontAwesomeIcon icon={faEnvelope} className="input-icon" />
                  <input
                    type="email"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    placeholder="أدخل بريدك الإلكتروني"
                    required
                  />
                </div>
              </div>

              <div className="forgot-password-buttons">
                <button type="submit" className="reset-btn" disabled={loading}>
                  {loading ? "جاري الإرسال..." : "إرسال رابط الإعادة"}
                </button>
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={() => {
                    setShowForgotPassword(false);
                    setResetEmail("");
                    setError("");
                    setResetMessage("");
                  }}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
