// إنشاء جدول complete_orders من خلال التطبيق
// تشغيل هذا الملف في console المتصفح

async function createCompleteOrdersTable() {
  console.log("🔧 Creating complete_orders table...");
  
  try {
    // استخدام Supabase client من التطبيق
    const { supabase } = window;
    
    if (!supabase) {
      console.error("❌ Supabase client not found. Make sure you're running this in the app context.");
      return;
    }

    // إنشاء الجدول باستخدام SQL
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS "public"."complete_orders" (
        "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "order_id" UUID NOT NULL,
        "table_number" INTEGER NOT NULL,
        "customer_name" VARCHAR(255) DEFAULT '',
        "notes" TEXT DEFAULT '',
        "product_id" UUID,
        "product_name" VARCHAR(255) NOT NULL,
        "product_description" TEXT DEFAULT '',
        "unit_price" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        "quantity" INTEGER NOT NULL DEFAULT 1,
        "item_total" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        "tax_rate" DECIMAL(5,2) NOT NULL DEFAULT 15.00,
        "tax_amount" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        "total_with_tax" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
        "assigned_waiter" VARCHAR(255),
        "waiter_id" UUID,
        "twitter_handle" VARCHAR(100),
        "payment_method" VARCHAR(50),
        "is_paid" BOOLEAN DEFAULT FALSE,
        "created_at" TIMESTAMPTZ DEFAULT NOW(),
        "updated_at" TIMESTAMPTZ DEFAULT NOW()
      );
    `;

    console.log("📝 Executing SQL:", createTableSQL);

    // تنفيذ SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error("❌ Error creating table:", error);
      
      // محاولة بديلة: إنشاء الجدول باستخدام REST API
      console.log("🔄 Trying alternative method...");
      
      // إدراج سجل تجريبي لإنشاء الجدول تلقائياً
      const testRecord = {
        order_id: '00000000-0000-0000-0000-000000000000',
        table_number: 999,
        customer_name: 'Test Customer',
        notes: 'Test record to create table',
        product_name: 'Test Product',
        unit_price: 0.01,
        quantity: 1,
        status: 'test'
      };

      const { data: insertData, error: insertError } = await supabase
        .from('complete_orders')
        .insert([testRecord])
        .select();

      if (insertError) {
        console.error("❌ Alternative method failed:", insertError);
        console.log("💡 Please create the table manually using Supabase Dashboard");
        console.log("📋 Use the SQL from complete_orders_table.sql file");
        return false;
      } else {
        console.log("✅ Table created successfully using alternative method!");
        
        // حذف السجل التجريبي
        await supabase
          .from('complete_orders')
          .delete()
          .eq('order_id', '00000000-0000-0000-0000-000000000000');
        
        console.log("🧹 Test record cleaned up");
      }
    } else {
      console.log("✅ Table created successfully!");
    }

    // إضافة بيانات تجريبية
    console.log("📦 Adding sample data...");
    
    const sampleData = [
      {
        order_id: '11111111-1111-1111-1111-111111111111',
        table_number: 5,
        customer_name: 'أحمد محمد',
        notes: 'بدون سكر، قهوة قوية',
        product_name: 'قهوة تركية',
        product_description: 'قهوة بن عربي أصيلة مع الهيل',
        unit_price: 15.00,
        quantity: 2,
        status: 'pending',
        assigned_waiter: 'سالم أحمد',
        twitter_handle: '@ahmed_coffee'
      },
      {
        order_id: '11111111-1111-1111-1111-111111111111',
        table_number: 5,
        customer_name: 'أحمد محمد',
        notes: 'بدون سكر، قهوة قوية',
        product_name: 'كابتشينو',
        product_description: 'إسبريسو مع حليب مبخر ورغوة',
        unit_price: 18.00,
        quantity: 1,
        status: 'pending',
        assigned_waiter: 'سالم أحمد',
        twitter_handle: '@ahmed_coffee'
      },
      {
        order_id: '22222222-2222-2222-2222-222222222222',
        table_number: 3,
        customer_name: 'فاطمة علي',
        notes: 'سكر خفيف، حليب إضافي',
        product_name: 'لاتيه',
        product_description: 'إسبريسو مع حليب ساخن وطبقة رغوة',
        unit_price: 20.00,
        quantity: 1,
        status: 'confirmed',
        assigned_waiter: 'محمد سعد',
        twitter_handle: '@fatima_latte'
      }
    ];

    const { data: sampleInsert, error: sampleError } = await supabase
      .from('complete_orders')
      .insert(sampleData)
      .select();

    if (sampleError) {
      console.error("❌ Error inserting sample data:", sampleError);
    } else {
      console.log("✅ Sample data inserted successfully!");
      console.log("📊 Inserted records:", sampleInsert.length);
    }

    // التحقق من البيانات
    console.log("🔍 Checking inserted data...");
    
    const { data: checkData, error: checkError } = await supabase
      .from('complete_orders')
      .select('*')
      .limit(5);

    if (checkError) {
      console.error("❌ Error checking data:", checkError);
    } else {
      console.log("📋 Current data in complete_orders:");
      console.table(checkData);
    }

    return true;

  } catch (error) {
    console.error("❌ Unexpected error:", error);
    return false;
  }
}

// تشغيل الدالة
console.log("🚀 Starting complete_orders table creation...");
createCompleteOrdersTable().then(success => {
  if (success) {
    console.log("🎉 Complete! The complete_orders table is ready to use.");
    console.log("💡 Now refresh the page to see the orders with product names!");
  } else {
    console.log("❌ Failed to create table. Please check the errors above.");
  }
});

// تصدير الدالة للاستخدام اليدوي
window.createCompleteOrdersTable = createCompleteOrdersTable;
