import React, { useState, useEffect } from "react";
import { useOrder } from "./context/OrderContext";
import { useLanguage } from "./context/LanguageContext";
import MenuItem from "./components/MenuItem";
import CategoryTabs from "./components/CategoryTabs";
import AvieLogo from "./components/AvieLogo";
import { menuData } from "./data/menuData";
import dataService from "./services/dataService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserTie } from "@fortawesome/free-solid-svg-icons";
import "./styles/main.css";

const CustomerMenu = () => {
  const [activeCategory, setActiveCategory] = useState(() => {
    // تحديد الفئة الافتراضية حسب اللغة
    const savedLanguage = localStorage.getItem("preferred-language");
    return savedLanguage === "en" ? "All Categories" : "جميع الأصناف";
  });
  const [customerName, setCustomerName] = useState("");
  const [orderNotes, setOrderNotes] = useState("");
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [currentMenuData, setCurrentMenuData] = useState([]);
  const [loading, setLoading] = useState(true);
  const { state, actions } = useOrder();
  const { language, toggleLanguage, t } = useLanguage();

  // تطبيق ترتيب الفئات المحفوظ
  const applyCategoriesOrder = (categoriesArray) => {
    try {
      const savedOrder = localStorage.getItem("categoriesOrder");
      if (savedOrder) {
        const orderedCategories = JSON.parse(savedOrder);

        // إنشاء خريطة للترتيب
        const orderMap = {};
        orderedCategories.forEach((cat, index) => {
          orderMap[cat.name] = index;
        });

        // ترتيب الفئات حسب الترتيب المحفوظ
        const sortedCategories = [...categoriesArray].sort((a, b) => {
          const orderA =
            orderMap[a.category] !== undefined ? orderMap[a.category] : 999;
          const orderB =
            orderMap[b.category] !== undefined ? orderMap[b.category] : 999;
          return orderA - orderB;
        });

        return sortedCategories;
      }
    } catch (error) {
      console.error("Error applying categories order:", error);
    }

    return categoriesArray;
  };

  // تحميل المنتجات من Supabase
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const products = await dataService.getProducts();
        console.log("Loaded products:", products);

        if (products && products.length > 0) {
          setCurrentMenuData(products);
        } else {
          // تطبيق ترتيب الفئات على البيانات المحلية أيضاً
          setCurrentMenuData(applyCategoriesOrder(menuData));
        }
      } catch (error) {
        console.error("Error loading products:", error);
        setCurrentMenuData(menuData);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();

    // إعداد تحديث دوري كل 10 ثوانٍ للحصول على المنتجات الجديدة
    const interval = setInterval(async () => {
      try {
        const products = await dataService.getProducts();
        if (products && products.length > 0) {
          // البيانات من Supabase تأتي مرتبة بالفعل من dataService
          setCurrentMenuData(products);
        }
      } catch (error) {
        console.error("Error refreshing products:", error);
      }
    }, 10000); // كل 10 ثوانٍ

    // تنظيف الفاصل الزمني عند إلغاء تحميل المكون
    return () => {
      clearInterval(interval);
    };
  }, []);

  // تحديث الفئة النشطة عند تغيير اللغة
  useEffect(() => {
    if (
      activeCategory === "جميع الأصناف" ||
      activeCategory === "All Categories"
    ) {
      setActiveCategory(language === "en" ? "All Categories" : "جميع الأصناف");
    }
  }, [language]);

  // Get all products in a single array
  const getAllProducts = () => {
    const allProducts = [];
    currentMenuData.forEach((section) => {
      section.items.forEach((item) => {
        allProducts.push(item);
      });
    });
    return allProducts;
  };

  // Get translated category name
  const getTranslatedCategoryName = (categoryName) => {
    // البحث عن الفئة في البيانات المحملة للحصول على الترجمة
    const categoryData = currentMenuData.find(
      (cat) => cat.category === categoryName
    );
    if (categoryData && categoryData.categoryNameEn && language === "en") {
      return categoryData.categoryNameEn;
    }
    return categoryName;
  };

  // Filter products based on active category
  const getFilteredProducts = () => {
    const allProducts = getAllProducts();
    if (
      activeCategory === "جميع الأصناف" ||
      activeCategory === "All Categories"
    ) {
      return allProducts;
    }
    // Find the category section and return its items
    const categorySection = currentMenuData.find(
      (section) => section.category === activeCategory
    );
    return categorySection ? categorySection.items : [];
  };

  // Handle adding item to cart
  const handleAddToCart = (item, qty) => {
    actions.addToCart(item, qty);
  };

  // Handle checkout
  const handleCheckout = () => {
    if (state.cart.length === 0) return;

    setShowCheckoutModal(true);
  };

  // Confirm order
  const confirmOrder = () => {
    const subtotal = state.cart.reduce(
      (sum, item) => sum + item.qty * item.price,
      0
    );
    const tax = subtotal * 0.15;
    const total = subtotal + tax;

    const orderData = {
      tableNumber: 1, // رقم طاولة افتراضي
      items: state.cart,
      subtotal,
      tax,
      total,
      customerName,
      notes: orderNotes,
    };

    actions.addOrder(orderData);
    actions.clearCart();
    setShowCheckoutModal(false);
    setCustomerName("");
    setOrderNotes("");

    alert(t.orderSuccess || "تم إرسال طلبك بنجاح! سيتم تحضيره قريباً.");
  };

  const filteredProducts = getFilteredProducts();

  return (
    <div className="customer-app-redesigned">
      {/* Modern Header */}
      <header className="modern-header">
        <div className="header-content">
          <div className="brand-section">
            <div className="main-logo">
              <AvieLogo size={80} />
            </div>
            <div className="brand-text">
              <p>{t.brandDescription || "قهوة مميزة • تجربة استثنائية"}</p>
            </div>
          </div>

          <div className="header-actions">
            {/* زر اللغة */}
            <button
              className="language-btn"
              onClick={toggleLanguage}
              title={
                language === "ar" ? "Switch to English" : "التبديل للعربية"
              }
            >
              {language === "ar" ? "EN" : "عر"}
            </button>

            {state.cart.length > 0 && (
              <div className="cart-preview">
                <div className="cart-badge">
                  {state.cart.reduce((sum, item) => sum + item.qty, 0)}
                </div>
                <div className="cart-amount">
                  {(
                    state.cart.reduce(
                      (sum, item) => sum + item.qty * item.price,
                      0
                    ) * 1.15
                  ).toFixed(2)}{" "}
                  {t.currency || "ر.س"}
                </div>
              </div>
            )}
            <a href="/login" className="staff-btn">
              <FontAwesomeIcon icon={faUserTie} />
              {t.staffLogin || "دخول الموظفين"}
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-container">
        {/* Welcome Section */}
        <section className="welcome-section">
          <div className="welcome-content">
            <h2>{t.welcomeTitle || "اكتشف قائمتنا المميزة"}</h2>
            <p>
              {t.welcomeSubtitle ||
                "اختر من مجموعة متنوعة من المشروبات والوجبات الشهية"}
            </p>
          </div>
        </section>

        {/* Menu Navigation */}
        <section className="menu-navigation">
          <CategoryTabs
            categories={[
              t.allCategories || "جميع الأصناف",
              ...new Set(currentMenuData.map((item) => item.category)),
            ]}
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            categoryData={currentMenuData}
          />
        </section>

        {/* Menu Content */}
        <div className="menu-content">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>{t.loadingProducts || "جاري تحميل المنتجات..."}</p>
            </div>
          ) : (
            <div className="products-grid">
              {filteredProducts.length > 0 ? (
                filteredProducts.map((item) => (
                  <MenuItem
                    key={item.id}
                    item={item}
                    addToCart={handleAddToCart}
                  />
                ))
              ) : (
                <div className="no-products">
                  <p>{t.noProducts || "لا توجد منتجات متاحة في هذا التصنيف"}</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Floating Cart Button */}
        {state.cart.length > 0 && (
          <div className="floating-cart-button" onClick={handleCheckout}>
            <div className="cart-icon-wrapper">
              <div className="cart-count-badge">
                {state.cart.reduce((sum, item) => sum + item.qty, 0)}
              </div>
              <span className="cart-text">{t.viewCart || "عرض السلة"}</span>
            </div>
            <div className="cart-total-amount">
              {(
                state.cart.reduce(
                  (sum, item) => sum + item.qty * item.price,
                  0
                ) * 1.15
              ).toFixed(2)}{" "}
              {t.currency || "ر.س"}
            </div>
          </div>
        )}
      </main>

      {showCheckoutModal && (
        <div
          className="checkout-modal-overlay"
          onClick={() => setShowCheckoutModal(false)}
        >
          <div className="checkout-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{t.confirmOrder || "تأكيد الطلب"}</h2>
              <button
                className="close-btn"
                onClick={() => setShowCheckoutModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="order-summary">
                <h3>{t.orderSummary || "ملخص الطلب"}</h3>
                {state.cart.map((item) => (
                  <div key={item.id} className="order-item">
                    <span>
                      {item.qty}x {item.name}
                    </span>
                    <span>
                      {(item.qty * item.price).toFixed(2)} {t.currency || "ر.س"}
                    </span>
                  </div>
                ))}

                <div className="order-totals">
                  <div className="total-row">
                    <span>{t.subtotal || "المجموع الفرعي:"}</span>
                    <span>
                      {state.cart
                        .reduce((sum, item) => sum + item.qty * item.price, 0)
                        .toFixed(2)}{" "}
                      {t.currency || "ر.س"}
                    </span>
                  </div>
                  <div className="total-row">
                    <span>{t.tax || "الضريبة (15%):"}</span>
                    <span>
                      {(
                        state.cart.reduce(
                          (sum, item) => sum + item.qty * item.price,
                          0
                        ) * 0.15
                      ).toFixed(2)}{" "}
                      {t.currency || "ر.س"}
                    </span>
                  </div>
                  <div className="total-row final-total">
                    <span>{t.total || "الإجمالي:"}</span>
                    <span>
                      {(
                        state.cart.reduce(
                          (sum, item) => sum + item.qty * item.price,
                          0
                        ) * 1.15
                      ).toFixed(2)}{" "}
                      {t.currency || "ر.س"}
                    </span>
                  </div>
                </div>
              </div>

              <div className="customer-info">
                <div className="form-group">
                  <label>{t.customerName || "الاسم (اختياري):"}</label>
                  <input
                    type="text"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    placeholder={t.customerNamePlaceholder || "أدخل اسمك"}
                  />
                </div>

                <div className="form-group">
                  <label>{t.orderNotes || "ملاحظات خاصة (اختياري):"}</label>
                  <textarea
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    placeholder={
                      t.orderNotesPlaceholder || "أي طلبات خاصة أو ملاحظات"
                    }
                    rows="3"
                  />
                </div>
              </div>

              <div className="modal-actions">
                <button className="confirm-order-btn" onClick={confirmOrder}>
                  {t.confirm || "تأكيد الطلب"}
                </button>
                <button
                  className="cancel-btn"
                  onClick={() => setShowCheckoutModal(false)}
                >
                  {t.cancel || "إلغاء"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

function CustomerApp() {
  return <CustomerMenu />;
}

export default CustomerApp;
