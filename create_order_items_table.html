<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول order_items</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .step h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left: 5px solid #FFC107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left: 5px solid #4CAF50;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border-left: 5px solid #f44336;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        #output {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .table-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            color: white;
        }
        th, td {
            padding: 8px;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗃️ إنشاء جدول order_items</h1>
        
        <div class="warning">
            <h3>⚠️ المشكلة المكتشفة</h3>
            <p>جدول <code>order_items</code> غير موجود في قاعدة البيانات، مما يمنع عرض تفاصيل الطلبات في صفحة الباريستا والنادل.</p>
        </div>

        <div class="step">
            <h3>🔍 الخطوة 1: التحقق من الجداول الموجودة</h3>
            <p>فحص الجداول المتوفرة في قاعدة البيانات</p>
            <button class="button" onclick="checkExistingTables()">🔍 فحص الجداول</button>
        </div>

        <div class="step">
            <h3>🏗️ الخطوة 2: إنشاء جدول order_items</h3>
            <p>إنشاء الجدول المفقود مع البنية الصحيحة</p>
            <button class="button" onclick="createOrderItemsTable()">🔧 إنشاء الجدول</button>
        </div>

        <div class="step">
            <h3>📦 الخطوة 3: إضافة بيانات تجريبية</h3>
            <p>إضافة عناصر للطلبات الموجودة</p>
            <button class="button" onclick="addSampleOrderItems()">📊 إضافة البيانات</button>
        </div>

        <div class="step">
            <h3>✅ الخطوة 4: التحقق من النتائج</h3>
            <p>التحقق من أن الجدول تم إنشاؤه والبيانات تعمل بشكل صحيح</p>
            <button class="button" onclick="verifyOrderItems()">🔍 التحقق من النتائج</button>
        </div>

        <div class="step">
            <h3>🚀 الخطوة 5: اختبار التطبيق</h3>
            <p>اختبار صفحة الباريستا للتأكد من ظهور تفاصيل الطلبات</p>
            <button class="button" onclick="testApplication()">🧪 اختبار التطبيق</button>
        </div>

        <div id="output"></div>
        <div id="table-display" class="table-display" style="display: none;"></div>

        <div class="success" style="display: none;" id="successMessage">
            <h3>🎉 تم إنشاء الجدول بنجاح!</h3>
            <p>جدول <code>order_items</code> جاهز الآن. ستظهر تفاصيل الطلبات في صفحة الباريستا والنادل!</p>
        </div>
    </div>

    <script>
        let output = document.getElementById('output');
        let tableDisplay = document.getElementById('table-display');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00bcd4',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            
            output.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">
                [${timestamp}] ${message}
            </div>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        async function checkExistingTables() {
            log('🔍 فحص الجداول الموجودة...', 'info');
            
            const tables = ['orders', 'order_items', 'products', 'categories'];
            
            for (const table of tables) {
                try {
                    const response = await fetch(`https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/${table}?select=count&limit=1`, {
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                        }
                    });
                    
                    if (response.ok) {
                        log(`✅ جدول ${table} موجود`, 'success');
                    } else {
                        log(`❌ جدول ${table} غير موجود أو لا يمكن الوصول إليه`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في فحص جدول ${table}: ${error.message}`, 'error');
                }
            }
        }

        async function createOrderItemsTable() {
            log('🏗️ إنشاء جدول order_items...', 'info');
            
            try {
                // محاولة إنشاء الجدول عبر إدراج سجل تجريبي
                const testRecord = {
                    order_id: '00000000-0000-0000-0000-000000000000',
                    product_id: 'test-product',
                    product_name: 'منتج تجريبي',
                    product_description: 'وصف تجريبي',
                    quantity: 1,
                    unit_price: 1.00
                };

                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/order_items', {
                    method: 'POST',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testRecord)
                });

                if (response.ok) {
                    log('✅ تم إنشاء جدول order_items بنجاح!', 'success');
                    
                    // حذف السجل التجريبي
                    await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/order_items?order_id=eq.00000000-0000-0000-0000-000000000000', {
                        method: 'DELETE',
                        headers: {
                            'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                        }
                    });
                    
                    log('🧹 تم تنظيف السجل التجريبي', 'info');
                    return true;
                } else {
                    const errorText = await response.text();
                    log('❌ فشل إنشاء الجدول: ' + errorText, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في إنشاء الجدول: ' + error.message, 'error');
                return false;
            }
        }

        async function addSampleOrderItems() {
            log('📦 إضافة عناصر للطلبات الموجودة...', 'info');
            
            const sampleItems = [
                // عناصر للطلب الأول
                {
                    order_id: '3e13729b-294b-4f36-b3c6-75d7e540a6fd',
                    product_id: '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8',
                    product_name: 'قهوة تركية',
                    product_description: 'قهوة بن عربي أصيلة مع الهيل',
                    quantity: 2,
                    unit_price: 15.00
                },
                {
                    order_id: '3e13729b-294b-4f36-b3c6-75d7e540a6fd',
                    product_id: '8e832479-2020-474d-9061-e68b7582dc29',
                    product_name: 'كابتشينو',
                    product_description: 'إسبريسو مع حليب مبخر ورغوة',
                    quantity: 1,
                    unit_price: 18.00
                },
                // عناصر للطلب الثاني
                {
                    order_id: '0fb01cae-9b51-442b-8107-ca5fbe20b2e6',
                    product_id: '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8',
                    product_name: 'قهوة تركية',
                    product_description: 'قهوة بن عربي أصيلة مع الهيل',
                    quantity: 1,
                    unit_price: 15.00
                },
                // عناصر للطلب الثالث
                {
                    order_id: 'ee3aaf92-8fb9-4704-885c-7a700bb965cb',
                    product_id: '42b8b323-cbce-42c3-af5e-8ace3ac3ecf8',
                    product_name: 'قهوة تركية',
                    product_description: 'قهوة بن عربي أصيلة مع الهيل',
                    quantity: 1,
                    unit_price: 15.00
                },
                {
                    order_id: 'ee3aaf92-8fb9-4704-885c-7a700bb965cb',
                    product_id: '8e832479-2020-474d-9061-e68b7582dc29',
                    product_name: 'كابتشينو',
                    product_description: 'إسبريسو مع حليب مبخر ورغوة',
                    quantity: 1,
                    unit_price: 18.00
                }
            ];

            try {
                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/order_items', {
                    method: 'POST',
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(sampleItems)
                });

                if (response.ok) {
                    const result = await response.json();
                    log(`✅ تم إدراج ${result.length} عنصر بنجاح!`, 'success');
                    return true;
                } else {
                    const errorText = await response.text();
                    log('❌ فشل إدراج العناصر: ' + errorText, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في إدراج العناصر: ' + error.message, 'error');
                return false;
            }
        }

        async function verifyOrderItems() {
            log('🔍 التحقق من النتائج...', 'info');
            
            try {
                // جلب الطلبات مع العناصر
                const response = await fetch('https://tjivtrkbjsesulrthxpr.supabase.co/rest/v1/orders?select=*,order_items(*)', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRqaXZ0cmtianNlc3VscnRoeHByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTIxNzQsImV4cCI6MjA2NTMyODE3NH0.OjMKF-_jNDq169ro5yIpXUiJPCYYdKEdNi2o6R5n0zw'
                    }
                });

                if (response.ok) {
                    const orders = await response.json();
                    log(`✅ تم جلب ${orders.length} طلب مع العناصر!`, 'success');
                    
                    // عرض النتائج في جدول
                    displayOrdersTable(orders);
                    
                    // التحقق من أن كل طلب له عناصر
                    let ordersWithItems = 0;
                    let totalItems = 0;
                    
                    orders.forEach(order => {
                        if (order.order_items && order.order_items.length > 0) {
                            ordersWithItems++;
                            totalItems += order.order_items.length;
                            log(`📋 طلب ${order.table_number}: ${order.order_items.length} عنصر`, 'info');
                        } else {
                            log(`⚠️ طلب ${order.table_number}: لا يحتوي على عناصر`, 'warning');
                        }
                    });
                    
                    log(`📊 إجمالي: ${ordersWithItems} طلب يحتوي على ${totalItems} عنصر`, 'success');
                    
                    if (ordersWithItems > 0) {
                        document.getElementById('successMessage').style.display = 'block';
                    }
                    
                    return true;
                } else {
                    log('❌ فشل جلب الطلبات: ' + response.status, 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في التحقق: ' + error.message, 'error');
                return false;
            }
        }

        function displayOrdersTable(orders) {
            let html = '<h3>📋 الطلبات مع العناصر</h3><table>';
            html += '<tr><th>رقم الطلب</th><th>الطاولة</th><th>العميل</th><th>المجموع</th><th>الحالة</th><th>العناصر</th></tr>';
            
            orders.forEach(order => {
                const itemsText = order.order_items ? 
                    order.order_items.map(item => `${item.product_name} x${item.quantity}`).join(', ') : 
                    'لا توجد عناصر';
                
                html += `<tr>
                    <td>${order.id.substring(0, 8)}...</td>
                    <td>${order.table_number}</td>
                    <td>${order.customer_name || 'بدون اسم'}</td>
                    <td>${order.total} ر.س</td>
                    <td>${order.status}</td>
                    <td>${itemsText}</td>
                </tr>`;
            });
            
            html += '</table>';
            tableDisplay.innerHTML = html;
            tableDisplay.style.display = 'block';
        }

        function testApplication() {
            log('🚀 فتح صفحة الباريستا للاختبار...', 'info');
            window.open('http://localhost:3001/barista', '_blank');
            log('💡 تحقق من ظهور تفاصيل الطلبات في الصفحة المفتوحة', 'info');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 مرحباً بك في أداة إنشاء جدول order_items', 'success');
            log('💡 اتبع الخطوات بالترتيب لحل مشكلة عدم ظهور تفاصيل الطلبات', 'info');
        };
    </script>
</body>
</html>
