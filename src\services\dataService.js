import { db, subscriptions, supabase } from "../lib/supabase";
import { menuData } from "../data/menuData";

// Data service using Supabase with fallback
export class DataService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.setupOnlineListener();
    this.fallbackData = {
      products: menuData,
      categories: [
        {
          id: 1,
          name: "مشروبات ساخنة",
          name_en: "Hot Drinks",
          display_order: 1,
          is_active: true,
        },
        {
          id: 2,
          name: "مشروبات باردة",
          name_en: "Cold Drinks",
          display_order: 2,
          is_active: true,
        },
        {
          id: 3,
          name: "العصائر",
          name_en: "Juices",
          display_order: 3,
          is_active: true,
        },
        {
          id: 4,
          name: "حلويات",
          name_en: "Desserts",
          display_order: 4,
          is_active: true,
        },
        {
          id: 5,
          name: "وجبات خفيفة",
          name_en: "Snacks",
          display_order: 5,
          is_active: true,
        },
        {
          id: 6,
          name: "المشروبات المميزة",
          name_en: "Premium Drinks",
          display_order: 6,
          is_active: true,
        },
        {
          id: 7,
          name: "الشيش",
          name_en: "Shisha",
          display_order: 7,
          is_active: true,
        },
      ],
    };
  }

  setupOnlineListener() {
    window.addEventListener("online", () => {
      this.isOnline = true;
    });

    window.addEventListener("offline", () => {
      this.isOnline = false;
    });
  }

  // Check if Supabase is available
  isSupabaseAvailable() {
    return (
      this.isOnline &&
      process.env.REACT_APP_SUPABASE_URL &&
      process.env.REACT_APP_SUPABASE_ANON_KEY
    );
  }

  // Products operations
  async getProducts() {
    if (!this.isSupabaseAvailable()) {
      console.warn("⚠️ Supabase not available, using fallback data");
      return this.getFallbackProducts();
    }

    try {
      const { data, error } = await db.products.getAll();
      if (error) {
        console.warn("⚠️ Supabase error, using fallback data:", error);
        return this.getFallbackProducts();
      }

      // إذا لم توجد منتجات في Supabase، استخدم البيانات المحلية
      if (!data || data.length === 0) {
        console.warn("⚠️ No products found in Supabase, using fallback data");
        return this.getFallbackProducts();
      }

      // Transform Supabase data to match current app structure with proper ordering
      return await this.transformProductsFromSupabase(data);
    } catch (error) {
      console.error("Error fetching products from Supabase:", error);
      console.warn("⚠️ Using fallback data");
      return this.getFallbackProducts();
    }
  }

  // الحصول على المنتجات المحلية مع ترتيب الفئات الصحيح
  getFallbackProducts() {
    const categories = this.getFallbackCategories();
    const categoryOrderMap = {};

    categories.forEach((cat) => {
      categoryOrderMap[cat.name] = cat.display_order;
    });

    // ترتيب المنتجات حسب ترتيب الفئات
    return this.fallbackData.products
      .map((section) => ({
        ...section,
        displayOrder: categoryOrderMap[section.category] || 999,
      }))
      .sort((a, b) => (a.displayOrder || 999) - (b.displayOrder || 999))
      .map(({ displayOrder, ...section }) => section);
  }

  async saveProduct(productData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      // Find category ID
      const { data: categories } = await db.categories.getAll();
      const category = categories?.find((c) => c.name === productData.category);

      const supabaseProduct = {
        name: productData.name,
        name_en: productData.nameEn || productData.name,
        description: productData.desc || productData.description,
        description_en: productData.descEn || productData.description_en || "",
        price: parseFloat(productData.price),
        image_url: productData.image,
        category_id: category?.id,
        is_popular: productData.popular || false,
        is_available: true,
      };

      const { data, error } = await db.products.create(supabaseProduct);
      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error saving product to Supabase:", error);
      throw error;
    }
  }

  async updateProduct(productId, productData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      // Find category ID
      const { data: categories } = await db.categories.getAll();
      const category = categories?.find((c) => c.name === productData.category);

      const supabaseProduct = {
        name: productData.name,
        name_en: productData.nameEn || productData.name,
        description: productData.desc || productData.description,
        description_en: productData.descEn || productData.description_en || "",
        price: parseFloat(productData.price),
        image_url: productData.image,
        category_id: category?.id,
        is_popular: productData.popular || false,
        is_available: true,
      };

      const { data, error } = await db.products.update(
        productId,
        supabaseProduct
      );
      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error updating product in Supabase:", error);
      throw error;
    }
  }

  async deleteProduct(productId) {
    // حذف المنتج من البيانات المحلية أولاً
    this.removeProductFromFallbackData(productId);

    if (!this.isSupabaseAvailable()) {
      console.warn(
        "⚠️ Supabase not available, product removed from local data only"
      );
      return { success: true, message: "Product removed from local data only" };
    }

    try {
      // التحقق من نوع الـ ID - إذا كان UUID فهو ID صحيح
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          productId
        );

      if (!isUUID) {
        // إذا لم يكن UUID، فهو منتج محلي فقط
        console.log(
          `Product ID "${productId}" is not a UUID, removed from local data only`
        );
        return { success: true, message: "Local product removed successfully" };
      }

      // إذا كان UUID، نحذف من Supabase أيضاً
      const { error } = await db.products.delete(productId);
      if (error) throw error;

      console.log(
        `✅ Product deleted from Supabase and local data: ${productId}`
      );
      return {
        success: true,
        message: "Product deleted from database and local data",
      };
    } catch (error) {
      console.error("Error deleting product from Supabase:", error);

      // إذا كان الخطأ متعلق بـ UUID غير صحيح، نعتبره منتج محلي
      if (error.code === "22P02" && error.message.includes("uuid")) {
        console.log(
          `Product ID "${productId}" is not a valid UUID, removed from local data only`
        );
        return { success: true, message: "Local product removed successfully" };
      }

      throw error;
    }
  }

  // دالة لحذف المنتج من البيانات المحلية
  removeProductFromFallbackData(productId) {
    try {
      for (let i = 0; i < this.fallbackData.products.length; i++) {
        const category = this.fallbackData.products[i];
        if (category.items) {
          const originalLength = category.items.length;
          category.items = category.items.filter(
            (item) => item.id !== productId
          );

          if (category.items.length < originalLength) {
            console.log(
              `✅ Removed product ID ${productId} from local category "${category.category}"`
            );
            return true;
          }
        }
      }
      console.log(`⚠️ Product ID ${productId} not found in local data`);
      return false;
    } catch (error) {
      console.error("Error removing product from local data:", error);
      return false;
    }
  }

  // Categories operations
  async getCategories() {
    if (!this.isSupabaseAvailable()) {
      console.warn("⚠️ Supabase not available, using fallback categories");
      return this.getFallbackCategories();
    }

    try {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .eq("is_active", true)
        .order("display_order", { ascending: true });

      if (error) {
        console.error("Error fetching categories from Supabase:", error);
        return this.getFallbackCategories();
      }

      return data && data.length > 0 ? data : this.getFallbackCategories();
    } catch (error) {
      console.error("Error connecting to Supabase:", error);
      return this.getFallbackCategories();
    }
  }

  // دالة لإعادة تعيين البيانات المحلية إلى الحالة الأصلية
  resetFallbackData() {
    console.log("🔄 Resetting fallback data to original state");
    this.fallbackData.products = [...menuData];
    console.log(
      "✅ Fallback data reset. Categories available:",
      this.fallbackData.products.map((p) => p.category)
    );
  }

  // استخراج الفئات من البيانات المحلية بشكل ديناميكي
  getFallbackCategories() {
    // التأكد من أن البيانات المحلية تحتوي على جميع الفئات
    const currentCategories = [
      ...new Set(this.fallbackData.products.map((item) => item.category)),
    ];

    console.log("📋 Current categories in fallback data:", currentCategories);

    // إذا كانت فئة "الشيش" مفقودة، أعد تعيين البيانات
    if (!currentCategories.includes("الشيش")) {
      console.warn("⚠️ 'الشيش' category missing, resetting fallback data");
      this.resetFallbackData();
    }

    const categoriesFromMenuData = [
      ...new Set(this.fallbackData.products.map((item) => item.category)),
    ];

    console.log("📋 Final categories from menu data:", categoriesFromMenuData);

    return categoriesFromMenuData
      .map((categoryName, index) => {
        // البحث عن الفئة في البيانات الافتراضية للحصول على الترجمة
        const existingCategory = this.fallbackData.categories.find(
          (cat) => cat.name === categoryName
        );

        return {
          id: existingCategory?.id || `fallback_${index + 1}`,
          name: categoryName,
          name_en:
            existingCategory?.name_en ||
            this.getCategoryEnglishName(categoryName),
          display_order: existingCategory?.display_order || index + 1,
          is_active: true,
          description: "",
          description_en: "",
        };
      })
      .sort((a, b) => a.display_order - b.display_order);
  }

  // ترجمة أسماء الفئات إلى الإنجليزية (دالة عامة)
  getCategoryEnglishName(arabicName) {
    const translations = {
      "مشروبات ساخنة": "Hot Drinks",
      "مشروبات باردة": "Cold Drinks",
      العصائر: "Juices",
      حلويات: "Desserts",
      "وجبات خفيفة": "Snacks",
      "المشروبات المميزة": "Premium Drinks",
      الشيش: "Shisha",
    };
    return translations[arabicName] || arabicName;
  }

  // دالة عامة للوصول إلى ترجمة الفئات (للاستخدام الخارجي)
  static getCategoryEnglishName(arabicName) {
    const translations = {
      "مشروبات ساخنة": "Hot Drinks",
      "مشروبات باردة": "Cold Drinks",
      العصائر: "Juices",
      حلويات: "Desserts",
      "وجبات خفيفة": "Snacks",
      "المشروبات المميزة": "Premium Drinks",
      الشيش: "Shisha",
    };
    return translations[arabicName] || arabicName;
  }

  async saveCategory(categoryData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const supabaseCategory = {
        name: categoryData.name,
        name_en: categoryData.name_en || categoryData.nameEn || "",
        description: categoryData.description || "",
        description_en:
          categoryData.description_en || categoryData.descriptionEn || "",
        display_order: categoryData.display_order || 0,
        is_active: true,
      };

      const { data, error } = await supabase
        .from("categories")
        .insert([supabaseCategory])
        .select()
        .single();

      if (error) throw error;

      console.log("✅ Category saved to Supabase successfully:", data);
      return data;
    } catch (error) {
      console.error("Error saving category to Supabase:", error);
      throw error;
    }
  }

  async updateCategory(categoryId, categoryData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const supabaseCategory = {
        name: categoryData.name,
        name_en: categoryData.name_en || categoryData.nameEn || "",
        description: categoryData.description || "",
        description_en:
          categoryData.description_en || categoryData.descriptionEn || "",
        display_order: categoryData.display_order || 0,
      };

      const { data, error } = await supabase
        .from("categories")
        .update(supabaseCategory)
        .eq("id", categoryId)
        .select()
        .single();

      if (error) throw error;

      console.log("✅ Category updated in Supabase successfully:", data);
      return data;
    } catch (error) {
      console.error("Error updating category in Supabase:", error);
      throw error;
    }
  }

  async deleteCategory(categoryId) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      // إذا كان categoryId هو اسم الفئة وليس ID، نحاول العثور على الفئة
      let actualCategoryId = categoryId;

      // التحقق من نوع الـ ID - إذا كان UUID فهو ID صحيح
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          categoryId
        );

      if (isUUID) {
        // إذا كان UUID، نتأكد من وجود الفئة
        const { data: categories, error: categoriesError } = await supabase
          .from("categories")
          .select("*")
          .eq("id", categoryId)
          .eq("is_active", true);

        if (categoriesError) {
          console.error(
            "❌ Error fetching categories from Supabase:",
            categoriesError
          );
          throw new Error(`خطأ في جلب الفئات: ${categoriesError.message}`);
        }

        if (!categories || categories.length === 0) {
          throw new Error(`لم يتم العثور على الفئة بالمعرف: ${categoryId}`);
        }

        actualCategoryId = categoryId;
      } else {
        // إذا لم يكن UUID، نبحث بالاسم
        const { data: categories, error: categoriesError } = await supabase
          .from("categories")
          .select("*")
          .eq("name", categoryId)
          .eq("is_active", true);

        if (categoriesError) {
          console.error(
            "❌ Error fetching categories from Supabase:",
            categoriesError
          );
          throw new Error(`خطأ في جلب الفئات: ${categoriesError.message}`);
        }

        if (!categories || categories.length === 0) {
          throw new Error(`لم يتم العثور على الفئة: ${categoryId}`);
        }

        actualCategoryId = categories[0].id;
      }

      // التحقق من وجود منتجات في هذه الفئة
      const { data: products, error: productsError } =
        await db.products.getAll();
      if (productsError) {
        console.warn(
          "⚠️ Could not check for products in category:",
          productsError
        );
      }

      // عد المنتجات في هذه الفئة
      const productsInCategory =
        products?.filter(
          (product) => product.category_id === actualCategoryId
        ) || [];

      if (productsInCategory.length > 0) {
        throw new Error(
          `لا يمكن حذف هذه الفئة لأنها تحتوي على ${productsInCategory.length} منتج. يرجى حذف أو نقل المنتجات أولاً.`
        );
      }

      // حذف الفئة (soft delete)
      const { error } = await supabase
        .from("categories")
        .update({ is_active: false })
        .eq("id", actualCategoryId);
      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error("Error deleting category from Supabase:", error);
      throw error;
    }
  }

  // دالة لحذف فئة كاملة من البيانات المحلية
  removeCategoryFromFallbackData(categoryName) {
    try {
      const originalLength = this.fallbackData.products.length;
      this.fallbackData.products = this.fallbackData.products.filter(
        (category) => category.category !== categoryName
      );

      if (this.fallbackData.products.length < originalLength) {
        console.log(`✅ Removed category "${categoryName}" from local data`);
        return true;
      }

      console.log(`⚠️ Category "${categoryName}" not found in local data`);
      return false;
    } catch (error) {
      console.error("Error removing category from local data:", error);
      return false;
    }
  }

  // دالة لحذف جميع منتجات فئة من البيانات المحلية
  removeAllProductsFromCategoryInFallbackData(categoryName) {
    try {
      let deletedCount = 0;
      for (let i = 0; i < this.fallbackData.products.length; i++) {
        const category = this.fallbackData.products[i];
        if (category.category === categoryName && category.items) {
          deletedCount = category.items.length;
          category.items = [];
          console.log(
            `✅ Removed ${deletedCount} products from local category "${categoryName}"`
          );
          return deletedCount;
        }
      }
      console.log(`⚠️ Category "${categoryName}" not found in local data`);
      return 0;
    } catch (error) {
      console.error(
        "Error removing products from category in local data:",
        error
      );
      return 0;
    }
  }

  // دالة جديدة لحذف الفئة مع جميع منتجاتها
  async deleteCategoryWithProducts(categoryId, categoryName) {
    // حذف الفئة من البيانات المحلية أولاً
    const localDeleted = this.removeCategoryFromFallbackData(categoryName);

    if (!this.isSupabaseAvailable()) {
      console.warn(
        "⚠️ Supabase not available, category removed from local data only"
      );
      return { success: true, deletedProducts: localDeleted ? "unknown" : 0 };
    }

    try {
      // إذا كان categoryId هو اسم الفئة وليس ID، نحاول العثور على الفئة
      let actualCategoryId = categoryId;

      // التحقق من نوع الـ ID - إذا كان UUID فهو ID صحيح
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          categoryId
        );

      if (isUUID) {
        // إذا كان UUID، نتأكد من وجود الفئة
        const { data: categories, error: categoriesError } = await supabase
          .from("categories")
          .select("*")
          .eq("id", categoryId)
          .eq("is_active", true);

        if (categoriesError) {
          console.error(
            "❌ Error fetching categories from Supabase:",
            categoriesError
          );
          throw new Error(`خطأ في جلب الفئات: ${categoriesError.message}`);
        }

        if (!categories || categories.length === 0) {
          throw new Error(`لم يتم العثور على الفئة بالمعرف: ${categoryId}`);
        }

        actualCategoryId = categoryId;
      } else {
        // إذا لم يكن UUID، نبحث بالاسم
        const { data: categories, error: categoriesError } = await supabase
          .from("categories")
          .select("*")
          .eq("name", categoryId)
          .eq("is_active", true);

        if (categoriesError) {
          console.error(
            "❌ Error fetching categories from Supabase:",
            categoriesError
          );
          throw new Error(`خطأ في جلب الفئات: ${categoriesError.message}`);
        }

        if (!categories || categories.length === 0) {
          // إذا لم توجد الفئة في قاعدة البيانات، فهي فئة محلية فقط
          console.log(
            `Category "${categoryName}" is local only, removing from local data`
          );
          return { success: true, deletedProducts: 0 };
        }

        actualCategoryId = categories[0].id;
      }

      // جلب جميع المنتجات في هذه الفئة
      const { data: products, error: productsError } =
        await db.products.getAll();
      if (productsError) {
        console.warn("⚠️ Could not fetch products in category:", productsError);
      }

      // البحث عن المنتجات في هذه الفئة
      const productsInCategory =
        products?.filter(
          (product) => product.category_id === actualCategoryId
        ) || [];

      console.log(
        `Found ${productsInCategory.length} products in category "${categoryName}"`
      );

      // حذف جميع المنتجات في الفئة أولاً
      let deletedProductsCount = 0;
      for (const product of productsInCategory) {
        try {
          // استخدام دالة deleteProduct المحسنة
          const result = await this.deleteProduct(product.id);
          if (result && result.success) {
            deletedProductsCount++;
            console.log(
              `✅ Deleted product: ${product.name} (${
                result.message || "Success"
              })`
            );
          } else {
            console.error(
              `❌ Failed to delete product: ${product.name} - Unknown error`
            );
          }
        } catch (error) {
          console.error(`❌ Error deleting product ${product.name}:`, error);
        }
      }

      // حذف الفئة (soft delete)
      const { error: deleteCategoryError } = await supabase
        .from("categories")
        .update({ is_active: false })
        .eq("id", actualCategoryId);

      if (deleteCategoryError) {
        console.error("Error deleting category:", deleteCategoryError);
        throw deleteCategoryError;
      }

      console.log(
        `✅ Successfully deleted category "${categoryName}" with ${deletedProductsCount} products`
      );
      return { success: true, deletedProducts: deletedProductsCount };
    } catch (error) {
      console.error(
        "Error deleting category with products from Supabase:",
        error
      );
      throw error;
    }
  }

  async updateCategoryOrder(categoryId, displayOrder) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    // التحقق من صحة المعاملات
    if (!categoryId || typeof categoryId !== "string") {
      throw new Error("Invalid category ID provided");
    }

    if (typeof displayOrder !== "number" || displayOrder < 1) {
      throw new Error("Invalid display order provided");
    }

    try {
      const { data, error } = await supabase
        .from("categories")
        .update({ display_order: displayOrder })
        .eq("id", categoryId)
        .select();

      if (error) {
        // التحقق من نوع الخطأ
        if (error.code === "PGRST116") {
          throw new Error(
            "Categories table does not exist. Please create the categories table first."
          );
        }
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error(`Category with ID ${categoryId} not found`);
      }

      console.log(
        `✅ Updated category order: ${categoryId} -> ${displayOrder}`
      );
      return { success: true, data: data[0] };
    } catch (error) {
      console.error("Error updating category order:", error);
      throw error;
    }
  }

  // Orders operations
  async getOrders() {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const { data, error } = await db.orders.getAll();
      if (error) throw error;

      // Transform orders
      return this.transformOrdersFromSupabase(data);
    } catch (error) {
      console.error("Error fetching orders from Supabase:", error);
      throw error;
    }
  }

  async saveOrder(orderData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      // Save order to Supabase
      const supabaseOrder = {
        table_number: orderData.tableNumber,
        customer_name: orderData.customerName,
        notes: orderData.notes,
        twitter_handle: orderData.twitterHandle,
        subtotal: orderData.subtotal,
        tax: orderData.tax,
        total: orderData.total,
        status: orderData.status,
        assigned_waiter: orderData.assignedWaiter,
      };

      const { data: order, error: orderError } = await db.orders.create(
        supabaseOrder
      );
      if (orderError) throw orderError;

      // Save order items
      const orderItems = orderData.items.map((item) => ({
        order_id: order.id,
        product_id: item.productId || item.id,
        quantity: item.qty || item.quantity, // إصلاح: استخدام qty من العربة
        unit_price: item.price,
        total_price: item.price * (item.qty || item.quantity),
      }));

      const { error: itemsError } = await db.orderItems.create(orderItems);
      if (itemsError) throw itemsError;

      return order;
    } catch (error) {
      console.error("Error saving order to Supabase:", error);
      throw error;
    }
  }

  async updateOrderStatus(orderId, status, additionalData = {}) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const updateData = { status, ...additionalData };
      const { data, error } = await db.orders.update(orderId, updateData);
      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error updating order in Supabase:", error);
      throw error;
    }
  }

  // Sales operations
  async getSales() {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const { data, error } = await db.sales.getAll();
      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error fetching sales from Supabase:", error);
      throw error;
    }
  }

  async saveSale(saleData) {
    if (!this.isSupabaseAvailable()) {
      throw new Error(
        "Supabase is not available. Please check your internet connection and configuration."
      );
    }

    try {
      const { data, error } = await db.sales.create(saleData);
      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error saving sale to Supabase:", error);
      throw error;
    }
  }

  // Data transformation helpers
  async transformProductsFromSupabase(supabaseProducts) {
    const categoriesMap = {};

    // جلب الفئات مع ترتيبها
    const categories = await this.getCategories();
    const categoryOrderMap = {};
    const categoryTranslationMap = {};

    categories.forEach((cat, index) => {
      categoryOrderMap[cat.name] = cat.display_order || index + 1;
      categoryTranslationMap[cat.name] = {
        nameEn: cat.name_en || "",
        descriptionEn: cat.description_en || "",
      };
    });

    supabaseProducts.forEach((product) => {
      const categoryName = product.categories?.name || "غير مصنف";
      const categoryInfo = categoryTranslationMap[categoryName] || {};

      if (!categoriesMap[categoryName]) {
        categoriesMap[categoryName] = {
          category: categoryName,
          categoryNameEn: categoryInfo.nameEn || "", // إضافة الترجمة الإنجليزية للفئة
          categoryDescriptionEn: categoryInfo.descriptionEn || "", // إضافة الوصف الإنجليزي للفئة
          items: [],
          displayOrder: categoryOrderMap[categoryName] || 999,
        };
      }

      categoriesMap[categoryName].items.push({
        id: product.id,
        name: product.name,
        nameEn: product.name_en || "", // إضافة الترجمة الإنجليزية للمنتج
        desc: product.description,
        descEn: product.description_en || "", // إضافة الوصف الإنجليزي للمنتج
        price: product.price,
        image: product.image_url,
        popular: product.is_popular,
        category: categoryName, // إضافة الفئة للمنتج الفردي
      });
    });

    // إضافة الفئات التي لا تحتوي على منتجات من Supabase ولكن موجودة في البيانات المحلية
    const localCategories = this.getFallbackCategories();
    localCategories.forEach((localCat) => {
      if (!categoriesMap[localCat.name]) {
        // البحث عن المنتجات المحلية لهذه الفئة
        const localProducts = this.fallbackData.products
          .filter((section) => section.category === localCat.name)
          .flatMap((section) =>
            section.items.map((item) => ({
              ...item,
              category: section.category,
            }))
          );

        if (localProducts.length > 0) {
          categoriesMap[localCat.name] = {
            category: localCat.name,
            categoryNameEn: localCat.name_en,
            categoryDescriptionEn: localCat.description_en || "",
            items: localProducts,
            displayOrder: localCat.display_order,
          };
        }
      }
    });

    // ترتيب الفئات حسب display_order
    const sortedCategories = Object.values(categoriesMap).sort(
      (a, b) => (a.displayOrder || 999) - (b.displayOrder || 999)
    );

    // إزالة displayOrder من النتيجة النهائية
    return sortedCategories.map(({ displayOrder, ...category }) => category);
  }

  transformOrdersFromSupabase(supabaseOrders) {
    return supabaseOrders.map((order) => ({
      id: order.id,
      tableNumber: order.table_number,
      customerName: order.customer_name,
      notes: order.notes,
      twitterHandle: order.twitter_handle,
      subtotal: order.subtotal,
      tax: order.tax,
      total: order.total,
      status: order.status,
      assignedWaiter: order.assigned_waiter,
      timestamp: order.created_at,
      items:
        order.order_items?.map((item) => ({
          id: item.product_id,
          name: item.products?.name,
          price: item.unit_price,
          quantity: item.quantity,
          image: item.products?.image_url,
        })) || [],
    }));
  }

  // Subscribe to real-time updates
  subscribeToOrders(callback) {
    if (this.isSupabaseAvailable()) {
      return subscriptions.subscribeToOrders((payload) => {
        console.log("Real-time order update:", payload);
        // Trigger callback with updated data
        this.getOrders().then(callback).catch(console.error);
      });
    }
    return null;
  }

  subscribeToProducts(callback) {
    if (this.isSupabaseAvailable()) {
      return subscriptions.subscribeToProducts((payload) => {
        console.log("Real-time product update:", payload);
        // Trigger callback with updated data
        this.getProducts().then(callback).catch(console.error);
      });
    }
    return null;
  }

  unsubscribe(subscription) {
    if (subscription) {
      subscriptions.unsubscribe(subscription);
    }
  }
}

// Create singleton instance
export const dataService = new DataService();
export default dataService;
